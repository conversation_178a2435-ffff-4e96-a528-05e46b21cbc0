package com.linkcircle.boss.module.charge.crm.web.subscribe.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 订阅详情返回结果 VO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "订阅详情返回结果")
public class ChargeSubscriptionsDetailVO {


    @Schema(description = "订阅ID")
    private Long id;

    @Schema(description = "客户ID")
    private Long customerId;

    @Schema(description = "客户名称")
    private String customerName;

    @Schema(description = "账户ID")
    private Long accountId;

    @Schema(description = "账户名称")
    private String accountName;

    @Schema(description = "钱包ID")
    private Long walletsId;

    @Schema(description = "钱包名称")
    private String walletsName;

    @Schema(description = "钱包币种")
    private String walletsCurrency;

    @Schema(description = "主体ID")
    private Long entityId;

    @Schema(description = "计划ID")
    private Long planId;

    @Schema(description = "计划名称")
    private String planName;

    @Schema(description = "合同ID")
    private Long contractId;

    @Schema(description = "合同名称")
    private String contractName;

    @Schema(description = "税率百分比")
    private BigDecimal rate;

    @Schema(description = "支付类型 0.预付费 1.后付费 字典: subscription_payment_type")
    private Integer paymentType;

    @Schema(description = "支付方式，0:现金，1：积分 字典: subscription_payment_options")
    private Integer paymentOptions;

    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "审批状态")
    private Integer bpmStatus;

    @Schema(description = "出账生成草稿 0.否 1.是")
    private Integer billDrafts;

    @Schema(description = "创建者")
    private String creator;

    @Schema(description = "创建时间")
    private Long createTime;

    @Schema(description = "更新者")
    private String updater;

    @Schema(description = "更新时间")
    private Long updateTime;

    @Schema(description = "下次出账时间")
    private Long nextBillTime;

    @Schema(description = "订阅,当前版本只会有一个，考虑后续版本可能新增多阶段订阅")
    private List<ChargeSubscriptionsTimeDetailsVO> subscriptions;

}