package com.linkcircle.boss.module.billing.web.detail.rate.income.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.linkcircle.boss.module.billing.web.data.model.vo.CyclePeriodResultVO;
import com.linkcircle.boss.module.billing.web.detail.rate.cost.model.vo.RateUsageVO;
import com.linkcircle.boss.module.billing.web.detail.rate.income.mapper.IncomeFixedRateCycleStatusMapper;
import com.linkcircle.boss.module.billing.web.detail.rate.income.mapper.IncomePackageRateUsageMapper;
import com.linkcircle.boss.module.billing.web.detail.rate.income.mapper.IncomeTierRateUsageMapper;
import com.linkcircle.boss.module.billing.web.detail.rate.income.mapper.IncomeUsageRateUsageMapper;
import com.linkcircle.boss.module.billing.web.detail.rate.income.model.entity.IncomeFixedRateCycleStatusDO;
import com.linkcircle.boss.module.billing.web.detail.rate.income.model.entity.IncomePackageRateUsageDO;
import com.linkcircle.boss.module.billing.web.detail.rate.income.model.entity.IncomeTierRateUsageDO;
import com.linkcircle.boss.module.billing.web.detail.rate.income.model.entity.IncomeUsageRateUsageDO;
import com.linkcircle.boss.module.billing.web.detail.rate.income.service.IncomeRateUsageManageService;
import com.linkcircle.boss.module.billing.web.detail.rate.common.service.UnifiedRateUsageCacheService;
import com.linkcircle.boss.module.crm.enums.BillTypeEnum;
import com.linkcircle.boss.module.crm.enums.ChargeRateTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-06-23 09:02
 * @description 收入-费率用量管理服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IncomeRateUsageManageServiceImpl implements IncomeRateUsageManageService {

    private final IncomeFixedRateCycleStatusMapper incomeFixedRateCycleStatusMapper;
    private final IncomeTierRateUsageMapper incomeTierRateUsageMapper;
    private final IncomePackageRateUsageMapper incomePackageRateUsageMapper;
    private final IncomeUsageRateUsageMapper incomeUsageRateUsageMapper;
    private final UnifiedRateUsageCacheService unifiedRateUsageCacheService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRateUsage(CyclePeriodResultVO cyclePeriodResult,
                                   BigDecimal usage,
                                   String usageUnit,
                                   Boolean inTrial,
                                   Boolean existRecord) {
        // 试用期判断
        if (Boolean.FALSE.equals(inTrial)) {
            return true;
        }

        ChargeRateTypeEnum rateTypeEnum = cyclePeriodResult.getRateTypeEnum();
        return switch (rateTypeEnum) {
            case FIXED -> updateFixedRateStatus(cyclePeriodResult, usage, existRecord);
            case TIERED -> updateTierRateUsage(cyclePeriodResult, usage, usageUnit, existRecord);
            case PACKAGE -> updatePackageRateUsage(cyclePeriodResult, usage, usageUnit, existRecord);
            case USAGE -> updateUsageRateUsage(cyclePeriodResult, usage, usageUnit, existRecord);
        };
    }

    @Override
    public RateUsageVO getRateUsage(Integer rateType,
                                    Long subscriptionId,
                                    Long accountId,
                                    Long serviceId,
                                    String billingCycle) {
        ChargeRateTypeEnum rateTypeEnum = ChargeRateTypeEnum.getByType(rateType);
        return switch (rateTypeEnum) {
            case FIXED -> getFixedRateUsage(subscriptionId, serviceId, billingCycle);
            case TIERED -> getTierRateUsage(subscriptionId, serviceId, billingCycle);
            case PACKAGE -> getPackageRateUsage(subscriptionId, serviceId, billingCycle);
            case USAGE -> getUsageRateUsage(subscriptionId, serviceId, billingCycle);
        };
    }

    @Override
    public RateUsageVO getRateUsage(ChargeRateTypeEnum rateType, CyclePeriodResultVO cyclePeriodResult) {
        return getRateUsage(rateType.getType(),
                cyclePeriodResult.getSubscriptionId(),
                cyclePeriodResult.getAccountId(),
                cyclePeriodResult.getServiceId(),
                cyclePeriodResult.getBillingCycle());
    }

    @Override
    public boolean isFixedRateBilled(Long subscriptionId, Long serviceId, String billingCycle) {
        LambdaQueryWrapper<IncomeFixedRateCycleStatusDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(IncomeFixedRateCycleStatusDO::getId);
        queryWrapper.eq(IncomeFixedRateCycleStatusDO::getServiceId, serviceId)
                .eq(IncomeFixedRateCycleStatusDO::getSubscriptionId, subscriptionId)
                .eq(IncomeFixedRateCycleStatusDO::getBillingCycle, billingCycle);

        IncomeFixedRateCycleStatusDO statusDO = incomeFixedRateCycleStatusMapper.selectOne(queryWrapper);
        return statusDO != null;
    }

    @Override
    public boolean isTieredRateBilled(Long subscriptionId, Long serviceId, String billingCycle) {
        LambdaQueryWrapper<IncomeTierRateUsageDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(IncomeTierRateUsageDO::getId);
        queryWrapper.eq(IncomeTierRateUsageDO::getServiceId, serviceId)
                .eq(IncomeTierRateUsageDO::getSubscriptionId, subscriptionId)
                .eq(IncomeTierRateUsageDO::getBillingCycle, billingCycle);

        IncomeTierRateUsageDO statusDO = incomeTierRateUsageMapper.selectOne(queryWrapper);
        return statusDO != null;
    }

    /**
     * 更新固定费率状态
     */
    private boolean updateFixedRateStatus(CyclePeriodResultVO cyclePeriodResult,
                                          BigDecimal usage,
                                          Boolean existRecord) {
        String billingCycle = cyclePeriodResult.getBillingCycle();
        Long subscriptionId = cyclePeriodResult.getSubscriptionId();
        Long serviceId = cyclePeriodResult.getServiceId();
        String currency = cyclePeriodResult.getCurrency();

        if (Boolean.TRUE.equals(existRecord)) {
            return true;
        }

        // 记录不存在，直接新增
        IncomeFixedRateCycleStatusDO statusDO = IncomeFixedRateCycleStatusDO.builder()
                .subscriptionId(subscriptionId)
                .accountId(cyclePeriodResult.getAccountId())
                .serviceId(serviceId)
                .billingCycle(billingCycle)
                .billingCycleStart(cyclePeriodResult.getCycleStartTime())
                .billingCycleEnd(cyclePeriodResult.getCycleEndTime())
                .totalUsage(usage)
                .currency(currency)
                .billingTime(System.currentTimeMillis())
                .createTime(System.currentTimeMillis())
                .build();

        int insertCount = incomeFixedRateCycleStatusMapper.insert(statusDO);
        log.info("创建收入固定费率周期状态记录, subscriptionId: {}, billingCycle: {}, usage: {}, result: {}",
                subscriptionId, billingCycle, usage, insertCount > 0 ? "成功" : "失败");
        return insertCount > 0;
    }

    /**
     * 更新阶梯费率用量
     */
    private boolean updateTierRateUsage(CyclePeriodResultVO cyclePeriodResult,
                                        BigDecimal usage,
                                        String usageUnit,
                                        Boolean existRecord) {
        String billingCycle = cyclePeriodResult.getBillingCycle();
        Long subscriptionId = cyclePeriodResult.getSubscriptionId();
        Long serviceId = cyclePeriodResult.getServiceId();
        String currency = cyclePeriodResult.getCurrency();

        if (Boolean.TRUE.equals(existRecord)) {
            return true;
        }

        // 记录不存在，直接新增
        IncomeTierRateUsageDO usageDO = IncomeTierRateUsageDO.builder()
                .subscriptionId(subscriptionId)
                .accountId(cyclePeriodResult.getAccountId())
                .serviceId(serviceId)
                .billingCycle(billingCycle)
                .billingCycleStart(cyclePeriodResult.getCycleStartTime())
                .billingCycleEnd(cyclePeriodResult.getCycleEndTime())
                .totalUsage(usage)
                .usageUnit(usageUnit)
                .currency(currency)
                .billingTime(System.currentTimeMillis())
                .createTime(System.currentTimeMillis())
                .build();

        int insertCount = incomeTierRateUsageMapper.insert(usageDO);
        log.info("创建收入阶梯费率用量记录, subscriptionId: {}, billingCycle: {}, usage: {}, result: {}",
                subscriptionId, billingCycle, usage, insertCount > 0 ? "成功" : "失败");
        return insertCount > 0;
    }

    /**
     * 更新套餐费率用量（Redis缓存优化版本）
     */
    private boolean updatePackageRateUsage(CyclePeriodResultVO cyclePeriodResult,
                                           BigDecimal usage,
                                           String usageUnit,
                                           Boolean existRecord) {
        String billingCycle = cyclePeriodResult.getBillingCycle();
        Long subscriptionId = cyclePeriodResult.getSubscriptionId();
        Long serviceId = cyclePeriodResult.getServiceId();

        try {
            // 1. 优先更新Redis缓存，失败则降级
            boolean cacheSuccess = unifiedRateUsageCacheService.updatePackageRateUsageCache(BillTypeEnum.INCOME, cyclePeriodResult, usage, usageUnit);
            if (!cacheSuccess) {
                log.warn("套餐费率用量缓存更新失败，降级为直接数据库操作, subscriptionId: {}, billingCycle: {}, usage: {}",
                        subscriptionId, billingCycle, usage);
                return fallbackToDirectDbOperation(cyclePeriodResult, usage, usageUnit, existRecord, "package");
            }

            // 2. 缓存更新成功，数据库同步由定时任务处理
            log.info("套餐费率用量缓存更新成功，数据库同步将由定时任务处理, subscriptionId: {}, billingCycle: {}, usage: {}",
                    subscriptionId, billingCycle, usage);
            return true;

        } catch (Exception e) {
            log.error("套餐费率用量更新异常，降级为直接数据库操作, subscriptionId: {}, billingCycle: {}, usage: {}",
                    subscriptionId, billingCycle, usage, e);
            return fallbackToDirectDbOperation(cyclePeriodResult, usage, usageUnit, existRecord, "package");
        }
    }

    /**
     * 更新按量费率用量（Redis缓存优化版本）
     */
    private boolean updateUsageRateUsage(CyclePeriodResultVO cyclePeriodResult,
                                         BigDecimal usage,
                                         String usageUnit,
                                         Boolean existRecord) {
        String billingCycle = cyclePeriodResult.getBillingCycle();
        Long subscriptionId = cyclePeriodResult.getSubscriptionId();
        Long serviceId = cyclePeriodResult.getServiceId();

        try {
            // 1. 优先更新Redis缓存，失败则降级
            boolean cacheSuccess = unifiedRateUsageCacheService.updateUsageRateUsageCache(BillTypeEnum.INCOME, cyclePeriodResult, usage, usageUnit);
            if (!cacheSuccess) {
                log.warn("按量费率用量缓存更新失败，降级为直接数据库操作, subscriptionId: {}, billingCycle: {}, usage: {}",
                        subscriptionId, billingCycle, usage);
                return fallbackToDirectDbOperation(cyclePeriodResult, usage, usageUnit, existRecord, "usage");
            }

            // 2. 缓存更新成功，数据库同步由定时任务处理
            log.info("按量费率用量缓存更新成功，数据库同步将由定时任务处理, subscriptionId: {}, billingCycle: {}, usage: {}",
                    subscriptionId, billingCycle, usage);
            return true;

        } catch (Exception e) {
            log.error("按量费率用量更新异常，降级为直接数据库操作, subscriptionId: {}, billingCycle: {}, usage: {}",
                    subscriptionId, billingCycle, usage, e);
            return fallbackToDirectDbOperation(cyclePeriodResult, usage, usageUnit, existRecord, "usage");
        }
    }

    private RateUsageVO getFixedRateUsage(Long subscriptionId, Long serviceId, String billingCycle) {
        LambdaQueryWrapper<IncomeFixedRateCycleStatusDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(IncomeFixedRateCycleStatusDO::getTotalUsage);
        queryWrapper.eq(IncomeFixedRateCycleStatusDO::getServiceId, serviceId)
                .eq(IncomeFixedRateCycleStatusDO::getSubscriptionId, subscriptionId)
                .eq(IncomeFixedRateCycleStatusDO::getBillingCycle, billingCycle);

        IncomeFixedRateCycleStatusDO statusDO = incomeFixedRateCycleStatusMapper.selectOne(queryWrapper);
        if (Objects.nonNull(statusDO)) {
            return RateUsageVO.build(statusDO.getTotalUsage(), true);
        }
        return RateUsageVO.build(BigDecimal.ZERO, false);
    }

    /**
     * 获取阶梯费率用量
     */
    private RateUsageVO getTierRateUsage(Long subscriptionId, Long serviceId, String billingCycle) {
        LambdaQueryWrapper<IncomeTierRateUsageDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(IncomeTierRateUsageDO::getTotalUsage);
        queryWrapper.eq(IncomeTierRateUsageDO::getServiceId, serviceId)
                .eq(IncomeTierRateUsageDO::getSubscriptionId, subscriptionId)
                .eq(IncomeTierRateUsageDO::getBillingCycle, billingCycle);

        IncomeTierRateUsageDO statusDO = incomeTierRateUsageMapper.selectOne(queryWrapper);
        if (Objects.nonNull(statusDO)) {
            return RateUsageVO.build(statusDO.getTotalUsage(), true);
        }
        return RateUsageVO.build(BigDecimal.ZERO, false);
    }

    /**
     * 获取套餐费率用量（优先从Redis缓存获取）
     */
    private RateUsageVO getPackageRateUsage(Long subscriptionId, Long serviceId, String billingCycle) {
        try {
            // 优先从缓存获取数据
            BigDecimal totalUsage = unifiedRateUsageCacheService.getRateUsageWithCache(
                    BillTypeEnum.INCOME, "package", serviceId, subscriptionId, billingCycle);

            boolean existRecord = totalUsage.compareTo(BigDecimal.ZERO) > 0;
            log.debug("获取收入套餐费率用量, subscriptionId: {}, serviceId: {}, billingCycle: {}, usage: {}, existRecord: {}",
                    subscriptionId, serviceId, billingCycle, totalUsage, existRecord);

            return RateUsageVO.build(totalUsage, existRecord);

        } catch (Exception e) {
            log.error("获取收入套餐费率用量异常，降级为直接数据库查询, subscriptionId: {}, serviceId: {}, billingCycle: {}",
                    subscriptionId, serviceId, billingCycle, e);

            // 异常情况下降级为直接数据库查询
            LambdaQueryWrapper<IncomePackageRateUsageDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.select(IncomePackageRateUsageDO::getTotalUsage);
            queryWrapper.eq(IncomePackageRateUsageDO::getServiceId, serviceId)
                    .eq(IncomePackageRateUsageDO::getSubscriptionId, subscriptionId)
                    .eq(IncomePackageRateUsageDO::getBillingCycle, billingCycle);

            IncomePackageRateUsageDO statusDO = incomePackageRateUsageMapper.selectOne(queryWrapper);
            if (Objects.nonNull(statusDO)) {
                return RateUsageVO.build(statusDO.getTotalUsage(), true);
            }
            return RateUsageVO.build(BigDecimal.ZERO, false);
        }
    }

    /**
     * 获取按量费率用量（优先从Redis缓存获取）
     */
    private RateUsageVO getUsageRateUsage(Long subscriptionId, Long serviceId, String billingCycle) {
        try {
            // 优先从缓存获取数据
            BigDecimal totalUsage = unifiedRateUsageCacheService.getRateUsageWithCache(
                    BillTypeEnum.INCOME, "usage", serviceId, subscriptionId, billingCycle);

            boolean existRecord = totalUsage.compareTo(BigDecimal.ZERO) > 0;
            log.debug("获取收入按量费率用量, subscriptionId: {}, serviceId: {}, billingCycle: {}, usage: {}, existRecord: {}",
                    subscriptionId, serviceId, billingCycle, totalUsage, existRecord);

            return RateUsageVO.build(totalUsage, existRecord);

        } catch (Exception e) {
            log.error("获取收入按量费率用量异常，降级为直接数据库查询, subscriptionId: {}, serviceId: {}, billingCycle: {}",
                    subscriptionId, serviceId, billingCycle, e);

            // 异常情况下降级为直接数据库查询
            LambdaQueryWrapper<IncomeUsageRateUsageDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.select(IncomeUsageRateUsageDO::getTotalUsage);
            queryWrapper.eq(IncomeUsageRateUsageDO::getServiceId, serviceId)
                    .eq(IncomeUsageRateUsageDO::getSubscriptionId, subscriptionId)
                    .eq(IncomeUsageRateUsageDO::getBillingCycle, billingCycle);

            IncomeUsageRateUsageDO statusDO = incomeUsageRateUsageMapper.selectOne(queryWrapper);
            if (Objects.nonNull(statusDO)) {
                return RateUsageVO.build(statusDO.getTotalUsage(), true);
            }
            return RateUsageVO.build(BigDecimal.ZERO, false);
        }
    }

    /**
     * 降级处理：直接操作数据库
     */
    private boolean fallbackToDirectDbOperation(CyclePeriodResultVO cyclePeriodResult,
                                              BigDecimal usage,
                                              String usageUnit,
                                              Boolean existRecord,
                                              String rateType) {
        String billingCycle = cyclePeriodResult.getBillingCycle();
        Long subscriptionId = cyclePeriodResult.getSubscriptionId();
        Long serviceId = cyclePeriodResult.getServiceId();
        String currency = cyclePeriodResult.getCurrency();
        long nowTime = System.currentTimeMillis();

        try {
            if ("package".equals(rateType)) {
                return fallbackPackageRateUsage(cyclePeriodResult, usage, usageUnit, existRecord, nowTime);
            } else if ("usage".equals(rateType)) {
                return fallbackUsageRateUsage(cyclePeriodResult, usage, usageUnit, existRecord, nowTime);
            }
            return false;
        } catch (Exception e) {
            log.error("降级数据库操作失败, rateType: {}, subscriptionId: {}, billingCycle: {}, usage: {}",
                    rateType, subscriptionId, billingCycle, usage, e);
            return false;
        }
    }

    /**
     * 降级处理：套餐费率用量直接数据库操作
     */
    private boolean fallbackPackageRateUsage(CyclePeriodResultVO cyclePeriodResult,
                                            BigDecimal usage,
                                            String usageUnit,
                                            Boolean existRecord,
                                            long nowTime) {
        String billingCycle = cyclePeriodResult.getBillingCycle();
        Long subscriptionId = cyclePeriodResult.getSubscriptionId();
        Long serviceId = cyclePeriodResult.getServiceId();
        String currency = cyclePeriodResult.getCurrency();

        if (Boolean.TRUE.equals(existRecord)) {
            int updateCount = incomePackageRateUsageMapper.updateTotalUsageByCondition(serviceId, subscriptionId, billingCycle, usage, nowTime);
            log.info("降级-更新收入套餐费率用量, subscriptionId: {}, billingCycle: {}, 新增用量: {}, result: {}",
                    subscriptionId, billingCycle, usage, updateCount > 0 ? "成功" : "失败");
            return updateCount > 0;
        }

        // 记录不存在，直接新增
        IncomePackageRateUsageDO usageDO = IncomePackageRateUsageDO.builder()
                .subscriptionId(subscriptionId)
                .accountId(cyclePeriodResult.getAccountId())
                .serviceId(serviceId)
                .billingCycle(billingCycle)
                .billingCycleStart(cyclePeriodResult.getCycleStartTime())
                .billingCycleEnd(cyclePeriodResult.getCycleEndTime())
                .totalUsage(usage)
                .usageUnit(usageUnit)
                .currency(currency)
                .billingTime(nowTime)
                .createTime(nowTime)
                .build();

        int insertCount = incomePackageRateUsageMapper.insert(usageDO);
        log.info("降级-创建收入套餐费率用量记录, subscriptionId: {}, billingCycle: {}, usage: {}, result: {}",
                subscriptionId, billingCycle, usage, insertCount > 0 ? "成功" : "失败");
        return insertCount > 0;
    }

    /**
     * 降级处理：按量费率用量直接数据库操作
     */
    private boolean fallbackUsageRateUsage(CyclePeriodResultVO cyclePeriodResult,
                                         BigDecimal usage,
                                         String usageUnit,
                                         Boolean existRecord,
                                         long nowTime) {
        String billingCycle = cyclePeriodResult.getBillingCycle();
        Long subscriptionId = cyclePeriodResult.getSubscriptionId();
        Long accountId = cyclePeriodResult.getAccountId();
        Long serviceId = cyclePeriodResult.getServiceId();
        String currency = cyclePeriodResult.getCurrency();

        if (Boolean.TRUE.equals(existRecord)) {
            int updateCount = incomeUsageRateUsageMapper.updateTotalUsageByCondition(serviceId, subscriptionId, billingCycle, usage, nowTime);
            log.info("降级-更新收入按量费率用量, subscriptionId: {}, billingCycle: {}, 新增用量: {}, result: {}",
                    subscriptionId, billingCycle, usage, updateCount > 0 ? "成功" : "失败");
            return updateCount > 0;
        }

        // 记录不存在，直接新增
        IncomeUsageRateUsageDO usageDO = IncomeUsageRateUsageDO.builder()
                .subscriptionId(subscriptionId)
                .accountId(accountId)
                .serviceId(serviceId)
                .billingCycle(billingCycle)
                .billingCycleStart(cyclePeriodResult.getCycleStartTime())
                .billingCycleEnd(cyclePeriodResult.getCycleEndTime())
                .totalUsage(usage)
                .usageUnit(usageUnit)
                .currency(currency)
                .billingTime(nowTime)
                .createTime(nowTime)
                .build();

        int insertCount = incomeUsageRateUsageMapper.insert(usageDO);
        log.info("降级-创建收入按量费率用量记录, subscriptionId: {}, billingCycle: {}, usage: {}, result: {}",
                subscriptionId, billingCycle, usage, insertCount > 0 ? "成功" : "失败");
        return insertCount > 0;
    }



}
