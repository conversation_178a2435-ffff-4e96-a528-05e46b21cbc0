package com.linkcircle.boss.module.billing.web.detail.rate.income.service.impl;

import com.linkcircle.boss.framework.common.constants.ChargeTopicConstant;
import com.linkcircle.boss.framework.common.util.json.JsonUtils;
import com.linkcircle.boss.module.billing.web.data.model.vo.CyclePeriodResultVO;
import com.linkcircle.boss.module.billing.web.detail.rate.income.mapper.IncomePackageRateUsageMapper;
import com.linkcircle.boss.module.billing.web.detail.rate.income.mapper.IncomeUsageRateUsageMapper;
import com.linkcircle.boss.module.billing.web.detail.rate.income.model.dto.RateUsageDbSyncDTO;
import com.linkcircle.boss.module.billing.web.detail.rate.income.model.entity.IncomePackageRateUsageDO;
import com.linkcircle.boss.module.billing.web.detail.rate.income.model.entity.IncomeUsageRateUsageDO;
import com.linkcircle.boss.module.billing.web.detail.rate.income.service.RateUsageDbSyncService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.common.message.MessageConst;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-08-05 16:21
 * @description 费率用量数据库同步服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RateUsageDbSyncServiceImpl implements RateUsageDbSyncService {

    private final RocketMQTemplate rocketMQTemplate;
    private final IncomePackageRateUsageMapper incomePackageRateUsageMapper;
    private final IncomeUsageRateUsageMapper incomeUsageRateUsageMapper;

    private static final String RATE_TYPE_PACKAGE = "package";
    private static final String RATE_TYPE_USAGE = "usage";
    private static final int DELAY_LEVEL = 14; // 5分钟延迟

    @Override
    public boolean sendDbSyncMessage(CyclePeriodResultVO cyclePeriodResult, BigDecimal usage, String usageUnit, String rateType) {
        try {
            RateUsageDbSyncDTO syncDTO = RateUsageDbSyncDTO.builder()
                    .serviceId(cyclePeriodResult.getServiceId())
                    .subscriptionId(cyclePeriodResult.getSubscriptionId())
                    .billingCycle(cyclePeriodResult.getBillingCycle())
                    .usage(usage)
                    .rateType(rateType)
                    .billingTime(System.currentTimeMillis())
                    .usageUnit(usageUnit)
                    .currency(cyclePeriodResult.getCurrency())
                    .accountId(cyclePeriodResult.getAccountId())
                    .cycleStartTime(cyclePeriodResult.getCycleStartTime())
                    .cycleEndTime(cyclePeriodResult.getCycleEndTime())
                    .build();

            String destination = ChargeTopicConstant.CHARGE_DELAY_TOPIC + ":" + ChargeTopicConstant.TAG_RATE_USAGE_DB_SYNC;
            
            Message<String> message = MessageBuilder
                    .withPayload(JsonUtils.toJsonString(syncDTO))
                    .setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL, DELAY_LEVEL)
                    .build();

            SendResult sendResult = rocketMQTemplate.syncSend(destination, message);
            
            if (SendStatus.SEND_OK.equals(sendResult.getSendStatus())) {
                log.info("发送费率用量数据库同步消息成功, rateType: {}, serviceId: {}, subscriptionId: {}, billingCycle: {}, msgId: {}", 
                        rateType, cyclePeriodResult.getServiceId(), cyclePeriodResult.getSubscriptionId(), 
                        cyclePeriodResult.getBillingCycle(), sendResult.getMsgId());
                return true;
            } else {
                log.error("发送费率用量数据库同步消息失败, rateType: {}, serviceId: {}, subscriptionId: {}, billingCycle: {}, sendResult: {}", 
                        rateType, cyclePeriodResult.getServiceId(), cyclePeriodResult.getSubscriptionId(), 
                        cyclePeriodResult.getBillingCycle(), sendResult);
                return false;
            }
            
        } catch (Exception e) {
            log.error("发送费率用量数据库同步消息异常, rateType: {}, cyclePeriodResult: {}, usage: {}, usageUnit: {}", 
                    rateType, cyclePeriodResult, usage, usageUnit, e);
            return false;
        }
    }

    @Override
    public boolean processDbSyncMessage(Long serviceId, Long subscriptionId, String billingCycle, BigDecimal usage,
                                      String rateType, Long billingTime, String usageUnit, String currency,
                                      Long accountId, Long cycleStartTime, Long cycleEndTime) {
        try {
            if (RATE_TYPE_PACKAGE.equals(rateType)) {
                return processPackageRateUsageSync(serviceId, subscriptionId, billingCycle, usage, billingTime, 
                                                 usageUnit, currency, accountId, cycleStartTime, cycleEndTime);
            } else if (RATE_TYPE_USAGE.equals(rateType)) {
                return processUsageRateUsageSync(serviceId, subscriptionId, billingCycle, usage, billingTime, 
                                               usageUnit, currency, accountId, cycleStartTime, cycleEndTime);
            } else {
                log.error("不支持的费率类型: {}", rateType);
                return false;
            }
        } catch (Exception e) {
            log.error("处理费率用量数据库同步消息异常, rateType: {}, serviceId: {}, subscriptionId: {}, billingCycle: {}", 
                    rateType, serviceId, subscriptionId, billingCycle, e);
            return false;
        }
    }

    /**
     * 处理套餐费率用量同步
     */
    private boolean processPackageRateUsageSync(Long serviceId, Long subscriptionId, String billingCycle, 
                                              BigDecimal usage, Long billingTime, String usageUnit, String currency,
                                              Long accountId, Long cycleStartTime, Long cycleEndTime) {
        try {
            // 先尝试更新
            int updateCount = incomePackageRateUsageMapper.updateTotalUsageByCondition(serviceId, subscriptionId, billingCycle, usage, billingTime);
            
            if (updateCount > 0) {
                log.info("更新套餐费率用量数据库记录成功, serviceId: {}, subscriptionId: {}, billingCycle: {}, usage: {}", 
                        serviceId, subscriptionId, billingCycle, usage);
                return true;
            } else {
                // 更新失败，尝试插入新记录
                IncomePackageRateUsageDO usageDO = IncomePackageRateUsageDO.builder()
                        .subscriptionId(subscriptionId)
                        .accountId(accountId)
                        .serviceId(serviceId)
                        .billingCycle(billingCycle)
                        .billingCycleStart(cycleStartTime)
                        .billingCycleEnd(cycleEndTime)
                        .totalUsage(usage)
                        .usageUnit(usageUnit)
                        .currency(currency)
                        .billingTime(billingTime)
                        .createTime(System.currentTimeMillis())
                        .build();

                int insertCount = incomePackageRateUsageMapper.insert(usageDO);
                log.info("插入套餐费率用量数据库记录, serviceId: {}, subscriptionId: {}, billingCycle: {}, usage: {}, result: {}", 
                        serviceId, subscriptionId, billingCycle, usage, insertCount > 0 ? "成功" : "失败");
                return insertCount > 0;
            }
        } catch (Exception e) {
            log.error("处理套餐费率用量数据库同步异常, serviceId: {}, subscriptionId: {}, billingCycle: {}, usage: {}", 
                    serviceId, subscriptionId, billingCycle, usage, e);
            return false;
        }
    }

    /**
     * 处理按量费率用量同步
     */
    private boolean processUsageRateUsageSync(Long serviceId, Long subscriptionId, String billingCycle, 
                                            BigDecimal usage, Long billingTime, String usageUnit, String currency,
                                            Long accountId, Long cycleStartTime, Long cycleEndTime) {
        try {
            // 先尝试更新
            int updateCount = incomeUsageRateUsageMapper.updateTotalUsageByCondition(serviceId, subscriptionId, billingCycle, usage, billingTime);
            
            if (updateCount > 0) {
                log.info("更新按量费率用量数据库记录成功, serviceId: {}, subscriptionId: {}, billingCycle: {}, usage: {}", 
                        serviceId, subscriptionId, billingCycle, usage);
                return true;
            } else {
                // 更新失败，尝试插入新记录
                IncomeUsageRateUsageDO usageDO = IncomeUsageRateUsageDO.builder()
                        .subscriptionId(subscriptionId)
                        .accountId(accountId)
                        .serviceId(serviceId)
                        .billingCycle(billingCycle)
                        .billingCycleStart(cycleStartTime)
                        .billingCycleEnd(cycleEndTime)
                        .totalUsage(usage)
                        .usageUnit(usageUnit)
                        .currency(currency)
                        .billingTime(billingTime)
                        .createTime(System.currentTimeMillis())
                        .build();

                int insertCount = incomeUsageRateUsageMapper.insert(usageDO);
                log.info("插入按量费率用量数据库记录, serviceId: {}, subscriptionId: {}, billingCycle: {}, usage: {}, result: {}", 
                        serviceId, subscriptionId, billingCycle, usage, insertCount > 0 ? "成功" : "失败");
                return insertCount > 0;
            }
        } catch (Exception e) {
            log.error("处理按量费率用量数据库同步异常, serviceId: {}, subscriptionId: {}, billingCycle: {}, usage: {}", 
                    serviceId, subscriptionId, billingCycle, usage, e);
            return false;
        }
    }
}
