package com.linkcircle.boss.module.system.web.websocket.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.linkcircle.boss.framework.common.enums.RpcConstants;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.common.util.cache.SystemCacheUtils;
import com.linkcircle.boss.module.system.api.websocket.WebSocketSenderApi;
import com.linkcircle.boss.module.system.api.websocket.dto.WebSocketSendReqDTO;
import io.github.kk01001.netty.message.MessageDispatcher;
import io.github.kk01001.redis.RedissonUtil;
import io.github.kk01001.util.JacksonUtil;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025-05-04 13:39:00
 * @description
 */
@Slf4j
@RestController
@RequestMapping(RpcConstants.RPC_API_PREFIX + "/websocket")
@Validated
@RequiredArgsConstructor
public class WebSocketSenderRpcApiController implements WebSocketSenderApi {

    private final MessageDispatcher messageDispatcher;
    private final RedissonUtil redissonUtil;

    @PostMapping("/send")
    @Operation(summary = "发送 WebSocket 消息")
    @Override
    public CommonResult<Boolean> send(@Valid @RequestBody WebSocketSendReqDTO message) {
        log.info("[WebSocket] send message: {}", message);
        String sessionId = message.getSessionId();
        // 指定sessionId
        if (StrUtil.isNotEmpty(sessionId)) {
            messageDispatcher.sendToSession(sessionId, JacksonUtil.toJson(message));
            return CommonResult.success(true);
        }

        // 指定userId
        if (Objects.nonNull(message.getUserId())) {
            String userSessionIdKey = SystemCacheUtils.getUserSessionIdKey(String.valueOf(message.getUserId()));
            Set<String> sessionIds = redissonUtil.smembers(userSessionIdKey);
            log.info("[WebSocket] userSessionIdKey: {}, sessionIds: {}", userSessionIdKey, sessionIds);
            if (CollUtil.isEmpty(sessionIds)) {
                return CommonResult.success(true);
            }
            for (String id : sessionIds) {
                messageDispatcher.sendToSession(id, JacksonUtil.toJson(message));
            }
            return CommonResult.success(true);
        }

        // 广播
        messageDispatcher.broadcast(JacksonUtil.toJson(message));
        return CommonResult.success(true);
    }
}
