package com.linkcircle.boss.module.charge.fee.web.payment.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.linkcircle.boss.module.charge.fee.web.payment.model.entity.UnifiedRechargeOrderRq;
import com.linkcircle.boss.module.charge.fee.web.payment.service.IUnifiedRechargeOrderRqService;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.github.kk01001.xxljob.annotations.XxlJobRegister;
import io.github.kk01001.xxljob.enums.ExecutorRouteStrategyEnum;
import io.github.kk01001.xxljob.enums.MisfireStrategyEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.List;

@Component
@Slf4j
public class UnifiedRechargeScanJob {

    @Autowired
    private IUnifiedRechargeOrderRqService unifiedRechargeOrderRqService;


    /**
     * 对超过统一支付下单时间的订单做二次扫描
     * 扫描 状态支付中 、超过 scanTime并且 isScanOrder=0 的订单
     */
    @XxlJob("scanUnifiedRechargeJobHandler")
    @XxlJobRegister(
            cron = "0 0/5 * * * ?",  // 修改为每5分钟执行一
            jobDesc = "统一充值支付订单状态确认查询任务",
            author = "admin",
            triggerStatus = 1,
            executorRouteStrategy = ExecutorRouteStrategyEnum.ROUND,
            misfireStrategy = MisfireStrategyEnum.DO_NOTHING
    )
    public void scanUnifiedRecharge() {
        log.info("[统一充值支付接口状态回调查询] 时间: {}", Calendar.getInstance().getTimeInMillis());

        Calendar instance = Calendar.getInstance();
        Long curTime = instance.getTimeInMillis();
        instance.add(Calendar.HOUR_OF_DAY, -3);
        Long beginTime = instance.getTimeInMillis();
        LambdaQueryWrapper<UnifiedRechargeOrderRq> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UnifiedRechargeOrderRq::getStatus, 1);
        queryWrapper.eq(UnifiedRechargeOrderRq::getIsScanOrder, 0);
        queryWrapper.between(UnifiedRechargeOrderRq::getScanTime, beginTime, curTime);

        List<UnifiedRechargeOrderRq> list = unifiedRechargeOrderRqService.list(queryWrapper);
        if (CollectionUtils.isNotEmpty(list)) {
            // 存在3小时内 到了扫描时间并且还是支付中 且是第一次扫描的订单
            for (UnifiedRechargeOrderRq unifiedRechargeOrderRq : list) {
                unifiedRechargeOrderRqService.doScanTask(unifiedRechargeOrderRq);
            }
        }

    }

}
