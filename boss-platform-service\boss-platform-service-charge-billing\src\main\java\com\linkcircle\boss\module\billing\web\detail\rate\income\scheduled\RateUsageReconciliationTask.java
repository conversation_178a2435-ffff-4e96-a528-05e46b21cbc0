package com.linkcircle.boss.module.billing.web.detail.rate.income.scheduled;

import com.linkcircle.boss.framework.web.context.EnableLoginContext;
import com.linkcircle.boss.module.billing.web.detail.rate.income.service.RateUsageMonitorService;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.github.kk01001.util.TraceIdUtil;
import io.github.kk01001.xxljob.annotations.XxlJobRegister;
import io.github.kk01001.xxljob.enums.ExecutorRouteStrategyEnum;
import io.github.kk01001.xxljob.enums.MisfireStrategyEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025-08-05 16:21
 * @description 费率用量数据对账定时任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RateUsageReconciliationTask {

    private final RateUsageMonitorService rateUsageMonitorService;

    @XxlJob("rateUsageReconciliationHandler")
    @XxlJobRegister(
            cron = "0 0 2 * * ?", // 每天凌晨2点执行
            jobDesc = "费率用量数据对账任务",
            author = "linshiqiang",
            triggerStatus = 1,
            executorRouteStrategy = ExecutorRouteStrategyEnum.ROUND,
            misfireStrategy = MisfireStrategyEnum.DO_NOTHING
    )
    public void rateUsageReconciliationHandler() {
        String traceId = TraceIdUtil.generateTraceId();
        TraceIdUtil.setTraceId(traceId);
        
        try {
            log.info("开始执行费率用量数据对账任务");
            
            // 设置登录用户上下文
            EnableLoginContext.setLoginUserId(1L);
            EnableLoginContext.setLoginUserType(1);

            // 执行数据一致性检查
            rateUsageMonitorService.checkDataConsistency();
            
            log.info("费率用量数据对账任务执行完成");
            
        } catch (Exception e) {
            log.error("费率用量数据对账任务执行异常", e);
        } finally {
            // 清理上下文
            EnableLoginContext.clear();
            TraceIdUtil.clear();
        }
    }
}
