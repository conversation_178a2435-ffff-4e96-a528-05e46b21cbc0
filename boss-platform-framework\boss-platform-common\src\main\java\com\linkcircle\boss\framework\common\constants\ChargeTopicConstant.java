package com.linkcircle.boss.framework.common.constants;

/**
 * <AUTHOR>
 * @date 2025-06-17 11:36
 * @description 计费mq主题常量
 */
public interface ChargeTopicConstant {

    /**
     * %DLQ%
     */
    String DLQ = "%DLQ%";

    /**
     * 计费普通主题
     */
    String CHARGE_NORMAL_TOPIC = "boss-platform-charge-normal-topic";

    /**
     * 支付流程重放
     */
    String CHARGE_DEDUCTION_REPLAY_TOPIC = "boss-platform-wallet-deduction-replay-topic";

    /**
     * 计费广播主题
     */
    String CHARGE_BROADCAST_TOPIC = "boss-platform-charge-broadcast-topic";

    /**
     * 计费顺序主题
     */
    String CHARGE_FIFO_TOPIC = "boss-platform-charge-fifo-topic";

    /**
     * 计费延时主题
     */
    String CHARGE_DELAY_TOPIC = "boss-platform-charge-delay-topic";

    /**
     * 计费事务主题
     */
    String CHARGE_TRANSACTION_TOPIC = "boss-platform-charge-transaction-topic";

    /**
     * MQ标签 - 接收收入话单主题
     */
    String TAG_RECEIVE_INCOME_BILL = "receive-income-bill";

    /**
     * Group标签 - 接收收入话单主题
     */
    String GROUP_RECEIVE_INCOME_BILL = "receive-income-bill-group";

    /**
     * Group标签 - 钱包支付账单主题
     */
    String GROUP_WALLET_PAYMENT_BILL = "wallet-payment-bill-group";

    /**
     * Group标签 - 钱包支付账单重放
     */
    String GROUP_WALLET_PAYMENT_REPLAY = "wallet-deduction-replay-group";

    String GROUP_WALLET_PAYMENT_REPLAY_DLQ = "wallet-deduction-replay-dlq-group";


    /**
     * MQ标签 - 接收成本话单主题
     */
    String TAG_RECEIVE_COST_BILL = "receive-cost-bill";

    /**
     * Group标签 - 接收成本话单主题
     */
    String GROUP_RECEIVE_COST_BILL = "receive-cost-bill-group";

    /**
     * MQ标签 - 钱包扣款通知
     */
    String TAG_WALLET_DEDUCTION_NOTICE = "wallet-deduction-notice";

    /**
     * MQ标签 - 预付费账单入库重试
     */
    String TAG_PREPAID_BILL_SAVE_DB_RETRY = "prepaid-bill-save-db-retry";

    /**
     * MQ标签 - 后付费账单入库重试
     */
    String TAG_POSTPAID_BILL_SAVE_DB_RETRY = "postpaid-bill-save-db-retry";

    /**
     * MQ标签 - 事务 收入按服务出账
     */
    String TAG_INCOME_BILL_SERVICE_OUT = "income-bill-service-out";

    /**
     * GROUP - 收入按服务出账
     */
    String GROUP_INCOME_BILL_SERVICE_OUT = "income-bill-service-out-group";

    /**
     * MQ标签 - 事务 陈本按服务出账
     */
    String TAG_COST_BILL_SERVICE_OUT = "cost-bill-service-out";

    /**
     * GROUP - 事务 陈本按服务出账
     */
    String GROUP_COST_BILL_SERVICE_OUT = "cost-bill-service-out-group";

    /**
     * MQ标签 - 下载任务 - 财务明细
     */
    String TAG_DOWNLOAD_TASK_FINANCE_DETAIL = "download-task-finance-detail";


    /**
     * Group标签 -下载任务 - 财务明细 主题
     */
    String GROUP_DOWNLOAD_TASK_FINANCE_DETAIL = "download-task-finance-detail-group";

    /**
     * MQ标签 - 缓存更新通知
     */
    String TAG_CACHE_UPDATE = "billing-cache-update";

    /**
     * Group标签 - 缓存更新通知
     */
    String GROUP_CACHE_UPDATE = "billing-cache-update-group";

    /**
     * MQ标签 - 收入按产品出账
     */
    String TAG_INCOME_BILL_PRODUCT_OUT = "income-bill-product-out";

    /**
     * GROUP - 收入按产品出账
     */
    String GROUP_INCOME_BILL_PRODUCT_OUT = "income-bill-product-out-group";

    /**
     * MQ标签 - 费率用量数据库同步
     */
    String TAG_RATE_USAGE_DB_SYNC = "rate-usage-db-sync";

    /**
     * GROUP - 费率用量数据库同步
     */
    String GROUP_RATE_USAGE_DB_SYNC = "rate-usage-db-sync-group";
}
