package com.linkcircle.boss.module.billing.web.detail.income.service.calculation.strategy.impl;

import com.linkcircle.boss.framework.common.exception.util.ServiceExceptionUtil;
import com.linkcircle.boss.module.billing.api.rate.model.dto.UsageBasedRateConfigDTO;
import com.linkcircle.boss.module.billing.constants.ErrorCodeConstants;
import com.linkcircle.boss.module.billing.constants.RateTypeConstant;
import com.linkcircle.boss.module.billing.enums.IncomeRateTypeEnum;
import com.linkcircle.boss.module.billing.enums.OriginalPriceRateTypeEnum;
import com.linkcircle.boss.module.billing.exception.BillingFailException;
import com.linkcircle.boss.module.billing.web.data.model.vo.CyclePeriodResultVO;
import com.linkcircle.boss.module.billing.web.data.service.CyclePeriodCalculateService;
import com.linkcircle.boss.module.billing.web.detail.income.model.dto.ServiceSubscriptionInfoDTO;
import com.linkcircle.boss.module.billing.web.detail.income.service.calculation.strategy.AbstractIncomeRateChargeStrategy;
import com.linkcircle.boss.module.billing.web.detail.income.service.calculation.strategy.context.IncomeRateChargeRequest;
import com.linkcircle.boss.module.billing.web.detail.income.service.calculation.strategy.context.IncomeRateChargeResponse;
import com.linkcircle.boss.module.billing.web.detail.rate.cost.model.vo.RateUsageVO;
import com.linkcircle.boss.module.billing.web.detail.rate.income.service.IncomeRateUsageManageService;
import com.linkcircle.boss.module.billing.web.detail.rate.strategy.context.OriginalPriceCalculateRequest;
import com.linkcircle.boss.module.billing.web.detail.rate.strategy.context.OriginalPriceCalculateResponse;
import com.linkcircle.boss.module.crm.api.customer.account.vo.CustomerAccountVO;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.AccountSubscriptionsVO;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon;
import com.linkcircle.boss.module.crm.enums.ChargeRateTypeEnum;
import io.github.kk01001.design.pattern.strategy.IStrategy;
import io.github.kk01001.design.pattern.strategy.StrategyFactory;
import io.github.kk01001.design.pattern.strategy.annotation.Strategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-19 15:16
 * @description 按量计费策略
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Strategy(strategyEnum = IncomeRateTypeEnum.class, strategyType = RateTypeConstant.USAGE)
public class IncomeUsageRateChargeStrategy extends AbstractIncomeRateChargeStrategy implements IStrategy<IncomeRateChargeRequest, IncomeRateChargeResponse> {

    private final CyclePeriodCalculateService cyclePeriodCalculateService;
    private final IncomeRateUsageManageService incomeRateUsageManageService;
    private final StrategyFactory strategyFactory;

    @Override
    protected CyclePeriodCalculateService getCyclePeriodCalculateService() {
        return cyclePeriodCalculateService;
    }

    @Override
    public IncomeRateChargeResponse execute(IncomeRateChargeRequest request) {
        log.info("开始执行按量计费策略");

        // 1. 获取基础数据
        ServiceSubscriptionInfoDTO serviceInfo = request.getServiceSubscriptionInfoDTO();
        AccountSubscriptionsVO.Service serviceConfig = serviceInfo.getServiceConfig();
        BigDecimal currentUsage = request.getUsage();
        // 2. 解析按量计费配置
        UsageBasedRateConfigDTO rateConfig = getUsageBasedRateConfigDTO(serviceConfig);

        // 3. 获取账户信息
        CustomerAccountVO customerAccountVO = request.getCustomerAccountVO();
        String accountCurrency = customerAccountVO.getCurrency();

        // 4. 计算计费周期
        Long businessTime = request.getBusinessTime();
        serviceInfo.getServiceConfig().setUnitPeriod(customerAccountVO.getBillingCycle());
        serviceInfo.getServiceConfig().setPeriod(1);
        CyclePeriodResultVO cyclePeriodResultVO = getBillingCycle(serviceInfo, customerAccountVO, businessTime);

        // 6. 获取当前周期累计用量（用于全额支付状态判断）
        RateUsageVO rateUsageVO = incomeRateUsageManageService.getRateUsage(ChargeRateTypeEnum.USAGE, cyclePeriodResultVO);
        BigDecimal previousUsage = rateUsageVO.getUsageCount();
        BigDecimal totalUsageWithCurrent = previousUsage.add(currentUsage);

        // 7. 计算目录价和优惠价（调用原价计算策略）
        OriginalPriceCalculateResponse calculateResponse = calculateOriginalPriceByStrategy(totalUsageWithCurrent, previousUsage, currentUsage,
                request.getUsageUnit(), rateConfig, accountCurrency, serviceConfig.getPaymentOptions(), serviceConfig.getCoupons(),
                getTaxRate(serviceInfo), request.enableTax());
        BigDecimal originalPrice = calculateResponse.getOriginalPrice();
        BigDecimal discountedPrice = calculateResponse.getDiscountedPrice();

        // 8. 计算优惠金额
        BigDecimal discountAmount = originalPrice.subtract(discountedPrice);

        // 10. 更新费率用量
        String usageUnit = calculateResponse.getUsageUnit();
        Boolean inTrial = calculateResponse.getInTrial();
        boolean updateSuccess = incomeRateUsageManageService.updateRateUsage(cyclePeriodResultVO, currentUsage, usageUnit, inTrial, rateUsageVO.getExist());
        if (!updateSuccess) {
            log.error("按量费率用量更新失败");
            throw new BillingFailException(ErrorCodeConstants.BILLING_CALCULATION_FAILED, "用量更新失败");
        }

        // 11. 构建响应结果
        IncomeRateChargeResponse response = IncomeRateChargeResponse.success();
        response.setSuccess(true);
        response.setCurrency(accountCurrency);
        response.setUsage(currentUsage);
        response.setUsageUnit(usageUnit);
        response.setCyclePeriodResultVO(cyclePeriodResultVO);
        response.setRateConfig(rateConfig);
        response.setCouponList(serviceConfig.getCoupons());
        convertChargeResponse(response, calculateResponse);

        log.info("按量计费完成，实际用量: {}, 目录价: {}, 优惠后价格: {}, 优惠金额: {}",
                currentUsage, originalPrice, discountedPrice, discountAmount);

        return response;
    }

    /**
     * 通过策略计算按量费率目录价和优惠价
     *
     * @param totalUsageWithCurrent 包含本次用量的总累计用量
     * @param previousUsage         之前的累计用量
     * @param currentUsage          本次用量
     * @param rateConfig            按量计费配置
     * @param currency              币种
     * @param paymentOptions        支付方式 0-现金 1-积分
     * @param couponList            优惠券列表
     * @return 本次计费金额
     */
    private OriginalPriceCalculateResponse calculateOriginalPriceByStrategy(BigDecimal totalUsageWithCurrent,
                                                                            BigDecimal previousUsage,
                                                                            BigDecimal currentUsage,
                                                                            String currentUsageUnit,
                                                                            UsageBasedRateConfigDTO rateConfig,
                                                                            String currency,
                                                                            Integer paymentOptions,
                                                                            List<Coupon> couponList,
                                                                            BigDecimal taxRate,
                                                                            boolean enableTax) {
        // 构建原价计算请求
        OriginalPriceCalculateRequest request = new OriginalPriceCalculateRequest();
        request.setTotalUsageWithCurrent(totalUsageWithCurrent);
        request.setPreviousUsage(previousUsage);
        request.setCurrentUsage(currentUsage);
        request.setCurrentUsageUnit(currentUsageUnit);
        request.setRateConfig(rateConfig);
        request.setCurrency(currency);
        request.setPaymentOptions(paymentOptions);
        request.setCouponList(couponList);
        request.setTaxRate(taxRate);
        request.setCalculateTaxEnabled(enableTax);

        // 调用原价计算策略
        OriginalPriceCalculateResponse response = strategyFactory.execute(OriginalPriceRateTypeEnum.class,
                OriginalPriceRateTypeEnum.USAGE.name(), request);

        if (response.getSuccess()) {
            return response;
        }
        log.error("response: {}, 原价计算失败", response);
        throw ServiceExceptionUtil.exception(ErrorCodeConstants.BILLING_CALCULATION_FAILED, response.getErrorMessage());
    }

}
