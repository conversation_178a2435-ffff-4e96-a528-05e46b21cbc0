package com.linkcircle.boss.module.billing.web.detail.rate.common.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.linkcircle.boss.framework.common.util.cache.ChargeCacheUtils;
import com.linkcircle.boss.module.billing.config.ChargeBillingProperties;
import com.linkcircle.boss.module.billing.web.data.model.vo.CyclePeriodResultVO;
import com.linkcircle.boss.module.billing.web.detail.rate.common.service.UnifiedRateUsageCacheService;
import com.linkcircle.boss.module.billing.web.detail.rate.cost.mapper.CostPackageRateUsageMapper;
import com.linkcircle.boss.module.billing.web.detail.rate.cost.mapper.CostUsageRateUsageMapper;
import com.linkcircle.boss.module.billing.web.detail.rate.cost.model.entity.CostPackageRateUsageDO;
import com.linkcircle.boss.module.billing.web.detail.rate.cost.model.entity.CostUsageRateUsageDO;
import com.linkcircle.boss.module.billing.web.detail.rate.income.mapper.IncomePackageRateUsageMapper;
import com.linkcircle.boss.module.billing.web.detail.rate.income.mapper.IncomeUsageRateUsageMapper;
import com.linkcircle.boss.module.billing.web.detail.rate.income.model.entity.IncomePackageRateUsageDO;
import com.linkcircle.boss.module.billing.web.detail.rate.income.model.entity.IncomeUsageRateUsageDO;
import com.linkcircle.boss.module.billing.web.detail.rate.income.service.impl.RateUsageSyncTracker;
import com.linkcircle.boss.module.crm.enums.BillTypeEnum;
import io.github.kk01001.redis.RedissonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-08-05 16:21
 * @description 统一费率用量缓存服务实现类 - 支持收入和成本两种类型
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UnifiedRateUsageCacheServiceImpl implements UnifiedRateUsageCacheService {

    private final RedissonUtil redissonUtil;
    private final RateUsageSyncTracker syncTracker;
    private final ChargeBillingProperties chargeBillingProperties;

    // 收入相关Mapper
    private final IncomePackageRateUsageMapper incomePackageRateUsageMapper;
    private final IncomeUsageRateUsageMapper incomeUsageRateUsageMapper;

    // 成本相关Mapper
    private final CostPackageRateUsageMapper costPackageRateUsageMapper;
    private final CostUsageRateUsageMapper costUsageRateUsageMapper;

    private static final String RATE_TYPE_PACKAGE = "package";
    private static final String RATE_TYPE_USAGE = "usage";

    // Hash字段名常量
    private static final String FIELD_TOTAL_USAGE = "total_usage";
    private static final String FIELD_BILLING_TIME = "billing_time";
    private static final String FIELD_USAGE_UNIT = "usage_unit";
    private static final String FIELD_CURRENCY = "currency";

    @Override
    public boolean updatePackageRateUsageCache(BillTypeEnum billType, CyclePeriodResultVO cyclePeriodResult,
                                               BigDecimal usage, String usageUnit) {
        return updateRateUsageCache(billType, RATE_TYPE_PACKAGE, cyclePeriodResult, usage, usageUnit);
    }

    @Override
    public boolean updateUsageRateUsageCache(BillTypeEnum billType, CyclePeriodResultVO cyclePeriodResult,
                                             BigDecimal usage, String usageUnit) {
        return updateRateUsageCache(billType, RATE_TYPE_USAGE, cyclePeriodResult, usage, usageUnit);
    }

    @Override
    public boolean loadUsageFromDatabase(BillTypeEnum billType, String rateType, Long serviceId,
                                         Long entityId, String billingCycle) {
        try {
            String cacheKey = buildCacheKey(billType, rateType, serviceId, entityId, billingCycle);

            if (RATE_TYPE_PACKAGE.equals(rateType)) {
                return loadPackageUsageFromDatabase(billType, cacheKey, serviceId, entityId, billingCycle);
            } else if (RATE_TYPE_USAGE.equals(rateType)) {
                return loadUsageUsageFromDatabase(billType, cacheKey, serviceId, entityId, billingCycle);
            }

            return false;
        } catch (Exception e) {
            log.error("从数据库加载用量数据到缓存失败, billType: {}, rateType: {}, serviceId: {}, entityId: {}, billingCycle: {}",
                    billType, rateType, serviceId, entityId, billingCycle, e);
            return false;
        }
    }

    @Override
    public BigDecimal getCachedTotalUsage(BillTypeEnum billType, String rateType, Long serviceId,
                                          Long entityId, String billingCycle) {
        try {
            String cacheKey = buildCacheKey(billType, rateType, serviceId, entityId, billingCycle);
            String totalUsageStr = redissonUtil.hget(cacheKey, FIELD_TOTAL_USAGE);

            if (StrUtil.isNotBlank(totalUsageStr)) {
                return new BigDecimal(totalUsageStr);
            }

            return null;
        } catch (Exception e) {
            log.error("获取缓存中的总用量失败, billType: {}, rateType: {}, serviceId: {}, entityId: {}, billingCycle: {}",
                    billType, rateType, serviceId, entityId, billingCycle, e);
            return null;
        }
    }

    /**
     * 通用的费率用量缓存更新方法
     */
    private boolean updateRateUsageCache(BillTypeEnum billType, String rateType, CyclePeriodResultVO cyclePeriodResult,
                                         BigDecimal usage, String usageUnit) {
        try {
            Long serviceId = cyclePeriodResult.getServiceId();
            Long entityId = getEntityId(billType, cyclePeriodResult);
            String billingCycle = cyclePeriodResult.getBillingCycle();
            String currency = cyclePeriodResult.getCurrency();
            long nowTime = System.currentTimeMillis();

            String cacheKey = buildCacheKey(billType, rateType, serviceId, entityId, billingCycle);

            // 检查缓存是否存在，如果不存在则从数据库加载
            if (!redissonUtil.exists(cacheKey)) {
                loadUsageFromDatabase(billType, rateType, serviceId, entityId, billingCycle);
            }

            // 使用Redis Hash原子操作更新用量
            Map<String, Object> hashMap = new HashMap<>();
            hashMap.put(FIELD_BILLING_TIME, String.valueOf(nowTime));
            hashMap.put(FIELD_USAGE_UNIT, usageUnit);
            hashMap.put(FIELD_CURRENCY, currency);

            // 原子递增总用量
            redissonUtil.hincrby(cacheKey, FIELD_TOTAL_USAGE, usage.longValue());
            redissonUtil.hmset(cacheKey, hashMap);

            // 设置TTL：周期结束时间 + 配置的缓冲期
            long bufferMillis = Duration.ofDays(chargeBillingProperties.getCache().getBufferDays()).toMillis();
            long ttlMillis = cyclePeriodResult.getCycleEndTime() - cyclePeriodResult.getCycleStartTime() + bufferMillis;
            redissonUtil.expire(cacheKey, Duration.ofMillis(ttlMillis));

            // 添加到待同步ZSet中
            syncTracker.addPendingSync(cacheKey);

            log.info("更新{}{}费率用量缓存成功, serviceId: {}, entityId: {}, billingCycle: {}, usage: {}",
                    billType.name().toLowerCase(), rateType, serviceId, entityId, billingCycle, usage);
            return true;

        } catch (Exception e) {
            log.error("更新{}{}费率用量缓存失败, cyclePeriodResult: {}, usage: {}, usageUnit: {}",
                    billType.name().toLowerCase(), rateType, cyclePeriodResult, usage, usageUnit, e);
            return false;
        }
    }

    @Override
    public BigDecimal getRateUsageWithCache(BillTypeEnum billType, String rateType, Long serviceId,
                                            Long entityId, String billingCycle) {
        try {
            // 检查是否启用查询缓存
            if (!chargeBillingProperties.getCache().getEnableQueryCache()) {
                log.debug("查询缓存已禁用，直接从数据库查询");
                return getRateUsageFromDatabase(billType, rateType, serviceId, entityId, billingCycle);
            }

            // 1. 先尝试从缓存获取
            BigDecimal cachedUsage = getCachedTotalUsage(billType, rateType, serviceId, entityId, billingCycle);
            if (cachedUsage != null) {
                log.debug("从缓存获取费率用量成功, billType: {}, rateType: {}, serviceId: {}, entityId: {}, billingCycle: {}, usage: {}",
                        billType, rateType, serviceId, entityId, billingCycle, cachedUsage);
                return cachedUsage;
            }

            // 2. 缓存不存在，从数据库查询
            BigDecimal dbUsage = getRateUsageFromDatabase(billType, rateType, serviceId, entityId, billingCycle);

            // 3. 将查询结果回写到缓存（如果数据库中有数据）
            if (dbUsage != null && dbUsage.compareTo(BigDecimal.ZERO) > 0) {
                cacheQueryResult(billType, rateType, serviceId, entityId, billingCycle, dbUsage);
            }

            log.debug("从数据库查询费率用量, billType: {}, rateType: {}, serviceId: {}, entityId: {}, billingCycle: {}, usage: {}",
                    billType, rateType, serviceId, entityId, billingCycle, dbUsage);

            return dbUsage != null ? dbUsage : BigDecimal.ZERO;

        } catch (Exception e) {
            log.error("获取费率用量异常, billType: {}, rateType: {}, serviceId: {}, entityId: {}, billingCycle: {}",
                    billType, rateType, serviceId, entityId, billingCycle, e);
            // 异常情况下直接从数据库查询
            BigDecimal dbUsage = getRateUsageFromDatabase(billType, rateType, serviceId, entityId, billingCycle);
            return dbUsage != null ? dbUsage : BigDecimal.ZERO;
        }
    }

    /**
     * 构建缓存key
     */
    private String buildCacheKey(BillTypeEnum billType, String rateType, Long serviceId, Long entityId, String billingCycle) {
        return ChargeCacheUtils.getUnifiedRateUsageCacheKey(
                billType.name().toLowerCase(), rateType, serviceId, entityId, billingCycle);
    }

    /**
     * 根据账单类型获取实体ID
     */
    private Long getEntityId(BillTypeEnum billType, CyclePeriodResultVO cyclePeriodResult) {
        return switch (billType) {
            case INCOME -> cyclePeriodResult.getSubscriptionId();
            case COST -> cyclePeriodResult.getSubscriptionId(); // 成本模块中subscriptionId实际是purchaseId
        };
    }

    /**
     * 从数据库加载套餐费率用量数据到缓存
     */
    private boolean loadPackageUsageFromDatabase(BillTypeEnum billType, String cacheKey, Long serviceId,
                                                 Long entityId, String billingCycle) {
        try {
            Map<String, Object> hashMap = new HashMap<>();

            if (billType == BillTypeEnum.INCOME) {
                LambdaQueryWrapper<IncomePackageRateUsageDO> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(IncomePackageRateUsageDO::getServiceId, serviceId)
                        .eq(IncomePackageRateUsageDO::getSubscriptionId, entityId)
                        .eq(IncomePackageRateUsageDO::getBillingCycle, billingCycle);

                IncomePackageRateUsageDO usageDO = incomePackageRateUsageMapper.selectOne(queryWrapper);
                if (Objects.nonNull(usageDO)) {
                    hashMap.put(FIELD_TOTAL_USAGE, usageDO.getTotalUsage().toString());
                    hashMap.put(FIELD_BILLING_TIME, String.valueOf(usageDO.getBillingTime()));
                    hashMap.put(FIELD_USAGE_UNIT, usageDO.getUsageUnit());
                    hashMap.put(FIELD_CURRENCY, usageDO.getCurrency());
                }
            } else {
                LambdaQueryWrapper<CostPackageRateUsageDO> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(CostPackageRateUsageDO::getServiceId, serviceId)
                        .eq(CostPackageRateUsageDO::getPurchaseId, entityId)
                        .eq(CostPackageRateUsageDO::getBillingCycle, billingCycle);

                CostPackageRateUsageDO usageDO = costPackageRateUsageMapper.selectOne(queryWrapper);
                if (Objects.nonNull(usageDO)) {
                    hashMap.put(FIELD_TOTAL_USAGE, usageDO.getTotalUsage().toString());
                    hashMap.put(FIELD_BILLING_TIME, String.valueOf(usageDO.getBillingTime()));
                    hashMap.put(FIELD_USAGE_UNIT, usageDO.getUsageUnit());
                    hashMap.put(FIELD_CURRENCY, usageDO.getCurrency());
                }
            }

            if (hashMap.isEmpty()) {
                // 数据库中不存在，设置默认值
                hashMap.put(FIELD_TOTAL_USAGE, "0");
                hashMap.put(FIELD_BILLING_TIME, String.valueOf(System.currentTimeMillis()));
            }

            redissonUtil.hmset(cacheKey, hashMap);
            log.info("从数据库加载{}套餐费率用量数据到缓存成功, cacheKey: {}", billType.name().toLowerCase(), cacheKey);
            return true;

        } catch (Exception e) {
            log.error("从数据库加载{}套餐费率用量数据失败, cacheKey: {}", billType.name().toLowerCase(), cacheKey, e);
            return false;
        }
    }

    /**
     * 从数据库加载按量费率用量数据到缓存
     */
    private boolean loadUsageUsageFromDatabase(BillTypeEnum billType, String cacheKey, Long serviceId,
                                               Long entityId, String billingCycle) {
        try {
            Map<String, Object> hashMap = new HashMap<>();

            if (billType == BillTypeEnum.INCOME) {
                LambdaQueryWrapper<IncomeUsageRateUsageDO> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(IncomeUsageRateUsageDO::getServiceId, serviceId)
                        .eq(IncomeUsageRateUsageDO::getSubscriptionId, entityId)
                        .eq(IncomeUsageRateUsageDO::getBillingCycle, billingCycle);

                IncomeUsageRateUsageDO usageDO = incomeUsageRateUsageMapper.selectOne(queryWrapper);
                if (Objects.nonNull(usageDO)) {
                    hashMap.put(FIELD_TOTAL_USAGE, usageDO.getTotalUsage().toString());
                    hashMap.put(FIELD_BILLING_TIME, String.valueOf(usageDO.getBillingTime()));
                    hashMap.put(FIELD_USAGE_UNIT, usageDO.getUsageUnit());
                    hashMap.put(FIELD_CURRENCY, usageDO.getCurrency());
                }
            } else {
                LambdaQueryWrapper<CostUsageRateUsageDO> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(CostUsageRateUsageDO::getServiceId, serviceId)
                        .eq(CostUsageRateUsageDO::getPurchaseId, entityId)
                        .eq(CostUsageRateUsageDO::getBillingCycle, billingCycle);

                CostUsageRateUsageDO usageDO = costUsageRateUsageMapper.selectOne(queryWrapper);
                if (Objects.nonNull(usageDO)) {
                    hashMap.put(FIELD_TOTAL_USAGE, usageDO.getTotalUsage().toString());
                    hashMap.put(FIELD_BILLING_TIME, String.valueOf(usageDO.getBillingTime()));
                    hashMap.put(FIELD_USAGE_UNIT, usageDO.getUsageUnit());
                    hashMap.put(FIELD_CURRENCY, usageDO.getCurrency());
                }
            }

            if (hashMap.isEmpty()) {
                // 数据库中不存在，设置默认值
                hashMap.put(FIELD_TOTAL_USAGE, "0");
                hashMap.put(FIELD_BILLING_TIME, String.valueOf(System.currentTimeMillis()));
            }

            redissonUtil.hmset(cacheKey, hashMap);
            log.info("从数据库加载{}按量费率用量数据到缓存成功, cacheKey: {}", billType.name().toLowerCase(), cacheKey);
            return true;

        } catch (Exception e) {
            log.error("从数据库加载{}按量费率用量数据失败, cacheKey: {}", billType.name().toLowerCase(), cacheKey, e);
            return false;
        }
    }

    /**
     * 从数据库查询费率用量
     */
    private BigDecimal getRateUsageFromDatabase(BillTypeEnum billType, String rateType, Long serviceId,
                                                Long entityId, String billingCycle) {
        try {
            if (RATE_TYPE_PACKAGE.equals(rateType)) {
                return getPackageRateUsageFromDatabase(billType, serviceId, entityId, billingCycle);
            } else if (RATE_TYPE_USAGE.equals(rateType)) {
                return getUsageRateUsageFromDatabase(billType, serviceId, entityId, billingCycle);
            }
            return BigDecimal.ZERO;
        } catch (Exception e) {
            log.error("从数据库查询费率用量异常, billType: {}, rateType: {}, serviceId: {}, entityId: {}, billingCycle: {}",
                    billType, rateType, serviceId, entityId, billingCycle, e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 从数据库查询套餐费率用量
     */
    private BigDecimal getPackageRateUsageFromDatabase(BillTypeEnum billType, Long serviceId, Long entityId, String billingCycle) {
        if (billType == BillTypeEnum.INCOME) {
            LambdaQueryWrapper<IncomePackageRateUsageDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(IncomePackageRateUsageDO::getServiceId, serviceId)
                    .eq(IncomePackageRateUsageDO::getSubscriptionId, entityId)
                    .eq(IncomePackageRateUsageDO::getBillingCycle, billingCycle);

            IncomePackageRateUsageDO usageDO = incomePackageRateUsageMapper.selectOne(queryWrapper);
            return usageDO != null ? usageDO.getTotalUsage() : BigDecimal.ZERO;
        } else {
            LambdaQueryWrapper<CostPackageRateUsageDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CostPackageRateUsageDO::getServiceId, serviceId)
                    .eq(CostPackageRateUsageDO::getPurchaseId, entityId)
                    .eq(CostPackageRateUsageDO::getBillingCycle, billingCycle);

            CostPackageRateUsageDO usageDO = costPackageRateUsageMapper.selectOne(queryWrapper);
            return usageDO != null ? usageDO.getTotalUsage() : BigDecimal.ZERO;
        }
    }

    /**
     * 从数据库查询按量费率用量
     */
    private BigDecimal getUsageRateUsageFromDatabase(BillTypeEnum billType, Long serviceId, Long entityId, String billingCycle) {
        if (billType == BillTypeEnum.INCOME) {
            LambdaQueryWrapper<IncomeUsageRateUsageDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(IncomeUsageRateUsageDO::getServiceId, serviceId)
                    .eq(IncomeUsageRateUsageDO::getSubscriptionId, entityId)
                    .eq(IncomeUsageRateUsageDO::getBillingCycle, billingCycle);

            IncomeUsageRateUsageDO usageDO = incomeUsageRateUsageMapper.selectOne(queryWrapper);
            return usageDO != null ? usageDO.getTotalUsage() : BigDecimal.ZERO;
        } else {
            LambdaQueryWrapper<CostUsageRateUsageDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CostUsageRateUsageDO::getServiceId, serviceId)
                    .eq(CostUsageRateUsageDO::getPurchaseId, entityId)
                    .eq(CostUsageRateUsageDO::getBillingCycle, billingCycle);

            CostUsageRateUsageDO usageDO = costUsageRateUsageMapper.selectOne(queryWrapper);
            return usageDO != null ? usageDO.getTotalUsage() : BigDecimal.ZERO;
        }
    }

    /**
     * 缓存查询结果
     */
    private void cacheQueryResult(BillTypeEnum billType, String rateType, Long serviceId,
                                  Long entityId, String billingCycle, BigDecimal usage) {
        try {
            String cacheKey = buildCacheKey(billType, rateType, serviceId, entityId, billingCycle);

            Map<String, Object> hashMap = new HashMap<>();
            hashMap.put(FIELD_TOTAL_USAGE, usage.toString());
            hashMap.put(FIELD_BILLING_TIME, String.valueOf(System.currentTimeMillis()));

            redissonUtil.hmset(cacheKey, hashMap);

            // 设置查询缓存TTL
            Duration queryTtl = Duration.ofHours(chargeBillingProperties.getCache().getQueryTtlHours());
            redissonUtil.expire(cacheKey, queryTtl);

            log.debug("缓存查询结果成功, billType: {}, rateType: {}, serviceId: {}, entityId: {}, billingCycle: {}, usage: {}",
                    billType, rateType, serviceId, entityId, billingCycle, usage);

        } catch (Exception e) {
            log.error("缓存查询结果失败, billType: {}, rateType: {}, serviceId: {}, entityId: {}, billingCycle: {}, usage: {}",
                    billType, rateType, serviceId, entityId, billingCycle, usage, e);
        }
    }
}
