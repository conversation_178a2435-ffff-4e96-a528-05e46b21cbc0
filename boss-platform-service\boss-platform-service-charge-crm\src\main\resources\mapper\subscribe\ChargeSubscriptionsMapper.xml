<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.linkcircle.boss.module.charge.crm.web.subscribe.mapper.ChargeSubscriptionsMapper">

    <select id="queryByPage"
            resultType="com.linkcircle.boss.module.charge.crm.web.subscribe.model.vo.ChargeSubscriptionsPageVO">
        SELECT
        sub.id,
        sub.customer_id AS customerId,
        sub.account_id AS accountId,
        sub.plan_id AS planId,
        sub.entity_id AS entityId,
        sub.rate,
        sub.payment_type AS paymentType,
        sub.STATUS,
        sub.bpm_status AS bpmStatus,
        sub.bill_drafts AS billDrafts,
        sub.create_time AS createTime,
        sub.creator,
        cont.contract_code AS contractCode,
        proj.project_code AS projectCode,
        ent.entity_name AS entityName,
        acct.billing_cycle AS billingCycle,
        acct.billing_day AS billingDay,
        acct.timezone,
        plan.plan_name AS planName,
        CONCAT(cust.customer_name, '/', acct.account_name) AS clientAccount
        FROM
        charge_subscriptions sub
        LEFT JOIN contract_info cont ON sub.contract_id = cont.id
        LEFT JOIN project_info proj ON cont.project_id = proj.id
        LEFT JOIN charge_entity_business ent ON sub.entity_id = ent.id
        LEFT JOIN charge_customer_info cust ON sub.customer_id = cust.id
        LEFT JOIN charge_customer_accounts_info acct ON sub.account_id = acct.id
        LEFT JOIN charge_plan plan ON sub.plan_id = plan.id
        <where>
            sub.deleted = 0
            <if test="query.entityId != null">
                AND sub.entity_id = #{query.entityId}
            </if>
            <if test="query.customerId != null">
                AND sub.customer_id = #{query.customerId}
            </if>
            <if test="query.customerIds != null and query.customerIds.size() > 0">
                AND sub.customer_id IN
                <foreach collection='query.customerIds' item='customerId' open='(' separator=',' close=')'>
                    #{customerId}
                </foreach>
            </if>
            <if test="query.accountId != null">
                AND sub.account_id = #{query.accountId}
            </if>
            <if test="query.status != null">
                AND sub.status = #{query.status}
            </if>
            <if test="query.statusList != null and query.statusList.size() > 0">
                <foreach collection='query.statusList' item='status' open='AND sub.status in (' separator=',' close=')'>
                    #{status}
                </foreach>
            </if>
            <if test="query.createStartTime != null">
                AND sub.createTime <![CDATA[ >= ]]> #{query.createStartTime}
            </if>
            <if test="query.createEndTime != null">
                AND sub.createTime <![CDATA[ <= ]]> #{query.createEndTime}
            </if>
        </where>
        ORDER BY sub.create_time DESC
    </select>

    <select id="detail"
            resultType="com.linkcircle.boss.module.charge.crm.web.subscribe.model.vo.ChargeSubscriptionsDetailVO">
        SELECT sub.id,
        sub.customer_id AS customerId,
        sub.account_id AS accountId,
        sub.wallets_id AS walletsId,
        sub.entity_id AS entityId,
        sub.plan_id AS planId,
        sub.contract_Id AS contractId,
        sub.rate,
        sub.payment_type AS paymentType,
        sub.payment_options AS paymentOptions,
        sub.STATUS,
        sub.bpm_status AS bpmStatus,
        sub.bill_drafts AS billDrafts,
        sub.create_time AS createTime,
        sub.creator,
        cont.contract_name AS contractName,
        proj.project_code AS projectCode,
        cust.customer_name AS customerName,
        ent.entity_name AS entityName,
        acct.account_name AS accountName,
        acct.timezone,
        plan.plan_name AS planName
        FROM charge_subscriptions sub
        LEFT JOIN contract_info cont ON sub.contract_id = cont.id
        LEFT JOIN project_info proj ON cont.project_id = proj.id
        LEFT JOIN charge_entity_business ent ON sub.entity_id = ent.id
        LEFT JOIN charge_customer_info cust ON sub.customer_id = cust.id
        LEFT JOIN charge_customer_accounts_info acct ON sub.account_id = acct.id
        LEFT JOIN charge_plan plan ON sub.plan_id = plan.id
        <where>
            sub.deleted = 0
            <if test="id != null and id != ''">
                AND sub.id = #{id}
            </if>
        </where>
    </select>

</mapper>