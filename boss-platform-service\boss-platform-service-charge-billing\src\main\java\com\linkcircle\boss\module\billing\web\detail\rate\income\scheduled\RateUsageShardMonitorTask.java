package com.linkcircle.boss.module.billing.web.detail.rate.income.scheduled;

import com.linkcircle.boss.framework.web.context.EnableLoginContext;
import com.linkcircle.boss.module.billing.web.detail.rate.income.service.impl.RateUsageSyncTracker;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.github.kk01001.util.TraceIdUtil;
import io.github.kk01001.xxljob.annotations.XxlJobRegister;
import io.github.kk01001.xxljob.enums.ExecutorRouteStrategyEnum;
import io.github.kk01001.xxljob.enums.MisfireStrategyEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025-08-05 16:21
 * @description 费率用量分片监控任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RateUsageShardMonitorTask {

    private final RateUsageSyncTracker syncTracker;

    @XxlJob("rateUsageShardMonitorHandler")
    @XxlJobRegister(
            cron = "0 */10 * * * ?", // 每10分钟执行一次
            jobDesc = "费率用量分片监控任务",
            author = "linshiqiang",
            triggerStatus = 1,
            executorRouteStrategy = ExecutorRouteStrategyEnum.ROUND,
            misfireStrategy = MisfireStrategyEnum.DO_NOTHING
    )
    public void rateUsageShardMonitorHandler() {
        String traceId = TraceIdUtil.generateTraceId();
        TraceIdUtil.setTraceId(traceId);
        
        try {
            log.info("开始执行费率用量分片监控任务");
            
            // 设置登录用户上下文
            EnableLoginContext.setLoginUserId(1L);
            EnableLoginContext.setLoginUserType(1);

            // 统计总体情况
            long totalPendingCount = syncTracker.getPendingCount();
            int shardCount = syncTracker.getShardCount();
            int maxShardSize = syncTracker.getMaxShardSize();
            
            log.info("费率用量分片总体状况 - 总待同步数量: {}, 分片数量: {}, 每分片最大容量: {}", 
                    totalPendingCount, shardCount, maxShardSize);
            
            // 统计各分片情况
            int nonEmptyShards = 0;
            long maxShardPendingCount = 0;
            long minShardPendingCount = Long.MAX_VALUE;
            int overloadedShards = 0; // 超过80%容量的分片
            
            StringBuilder shardDetails = new StringBuilder();
            shardDetails.append("分片详情: ");
            
            for (int i = 0; i < shardCount; i++) {
                long shardPendingCount = syncTracker.getShardPendingCount(i);
                
                if (shardPendingCount > 0) {
                    nonEmptyShards++;
                    maxShardPendingCount = Math.max(maxShardPendingCount, shardPendingCount);
                    minShardPendingCount = Math.min(minShardPendingCount, shardPendingCount);
                    
                    // 检查是否过载
                    if (shardPendingCount > maxShardSize * 0.8) {
                        overloadedShards++;
                    }
                    
                    // 只记录有数据的分片
                    if (shardDetails.length() > 10) { // 不是第一个
                        shardDetails.append(", ");
                    }
                    shardDetails.append(String.format("分片%d:%d", i, shardPendingCount));
                }
            }
            
            if (nonEmptyShards == 0) {
                log.info("所有分片都为空");
                return;
            }
            
            // 输出统计信息
            log.info("分片统计 - 非空分片数: {}/{}, 最大分片容量: {}, 最小分片容量: {}, 过载分片数: {}", 
                    nonEmptyShards, shardCount, maxShardPendingCount, 
                    minShardPendingCount == Long.MAX_VALUE ? 0 : minShardPendingCount, overloadedShards);
            
            // 输出分片详情（限制长度）
            if (shardDetails.length() > 500) {
                shardDetails.setLength(500);
                shardDetails.append("...");
            }
            log.info(shardDetails.toString());
            
            // 告警检查
            if (overloadedShards > 0) {
                log.warn("发现{}个过载分片，建议检查同步任务是否正常运行", overloadedShards);
            }
            
            if (totalPendingCount > 50000) {
                log.warn("待同步数据量过大: {}，建议增加同步频率或批次大小", totalPendingCount);
            }
            
            // 计算分片负载均衡度
            if (nonEmptyShards > 1) {
                double loadBalance = (double) minShardPendingCount / maxShardPendingCount;
                log.info("分片负载均衡度: {:.2f} (1.0为完全均衡)", loadBalance);
                
                if (loadBalance < 0.3) {
                    log.warn("分片负载不均衡，最大分片是最小分片的{:.1f}倍", 
                            (double) maxShardPendingCount / minShardPendingCount);
                }
            }
            
            log.info("费率用量分片监控任务执行完成");
            
        } catch (Exception e) {
            log.error("费率用量分片监控任务执行异常", e);
        } finally {
            // 清理上下文
            EnableLoginContext.clear();
            TraceIdUtil.clear();
        }
    }
}
