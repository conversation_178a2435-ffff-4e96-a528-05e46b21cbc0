package com.linkcircle.boss.module.billing.web.detail.rate.income.service;

import com.linkcircle.boss.module.billing.web.data.model.vo.CyclePeriodResultVO;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-08-05 16:21
 * @description 费率用量数据库同步服务接口
 */
public interface RateUsageDbSyncService {

    /**
     * 发送费率用量数据库同步消息
     *
     * @param cyclePeriodResult 周期结果
     * @param usage            用量
     * @param usageUnit        用量单位
     * @param rateType         费率类型
     * @return 是否发送成功
     */
    boolean sendDbSyncMessage(CyclePeriodResultVO cyclePeriodResult, BigDecimal usage, String usageUnit, String rateType);

    /**
     * 处理数据库同步消息
     *
     * @param serviceId      服务ID
     * @param subscriptionId 订阅ID
     * @param billingCycle   计费周期
     * @param usage          用量
     * @param rateType       费率类型
     * @param billingTime    计费时间
     * @param usageUnit      用量单位
     * @param currency       货币类型
     * @param accountId      账户ID
     * @param cycleStartTime 周期开始时间
     * @param cycleEndTime   周期结束时间
     * @return 是否处理成功
     */
    boolean processDbSyncMessage(Long serviceId, Long subscriptionId, String billingCycle, BigDecimal usage,
                               String rateType, Long billingTime, String usageUnit, String currency,
                               Long accountId, Long cycleStartTime, Long cycleEndTime);
}
