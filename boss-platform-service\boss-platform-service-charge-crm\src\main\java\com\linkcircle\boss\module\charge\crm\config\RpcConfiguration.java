package com.linkcircle.boss.module.charge.crm.config;

import com.linkcircle.boss.module.billing.api.cache.BillingCacheUpdateApi;
import com.linkcircle.boss.module.bpm.api.task.BpmProcessInstanceApi;
import com.linkcircle.boss.module.fee.api.bill.BillFeeApi;
import com.linkcircle.boss.module.fee.api.wallets.CustomerWalletApi;
import com.linkcircle.boss.module.system.api.config.ConfigApi;
import com.linkcircle.boss.module.system.api.file.FileApi;
import com.linkcircle.boss.module.system.api.websocket.WebSocketSenderApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025-06-05 9:42
 * @description 引入feign接口
 */
@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = {FileApi.class, WebSocketSenderApi.class, ConfigApi.class, CustomerWalletApi.class,
        BillFeeApi.class, BillingCacheUpdateApi.class, BpmProcessInstanceApi.class})
public class RpcConfiguration {
}
