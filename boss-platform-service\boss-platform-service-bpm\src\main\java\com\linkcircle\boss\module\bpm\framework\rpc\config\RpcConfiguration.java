package com.linkcircle.boss.module.bpm.framework.rpc.config;

import com.linkcircle.boss.module.crm.api.customer.subscriptions.ChargeSubscriptionsApi;
import com.linkcircle.boss.module.demo.api.DemoApi;
import com.linkcircle.boss.module.system.api.dept.DeptApi;
import com.linkcircle.boss.module.system.api.dept.PostApi;
import com.linkcircle.boss.module.system.api.dict.DictDataApi;
import com.linkcircle.boss.module.system.api.mail.MailSendApi;
import com.linkcircle.boss.module.system.api.notify.NotifyMessageSendApi;
import com.linkcircle.boss.module.system.api.permission.PermissionApi;
import com.linkcircle.boss.module.system.api.permission.RoleApi;
import com.linkcircle.boss.module.system.api.sms.SmsSendApi;
import com.linkcircle.boss.module.system.api.user.AdminUserApi;
import com.linkcircle.boss.module.system.api.websocket.WebSocketSenderApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = {RoleApi.class, DeptApi.class, PostApi.class, AdminUserApi.class,
        SmsSendApi.class, DictDataApi.class, DemoApi.class, PermissionApi.class, ChargeSubscriptionsApi.class,
        WebSocketSenderApi.class, NotifyMessageSendApi.class, MailSendApi.class})
public class RpcConfiguration {
}
