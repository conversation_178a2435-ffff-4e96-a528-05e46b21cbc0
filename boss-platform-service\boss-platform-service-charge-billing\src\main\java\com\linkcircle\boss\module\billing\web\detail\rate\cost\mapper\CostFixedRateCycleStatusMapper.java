package com.linkcircle.boss.module.billing.web.detail.rate.cost.mapper;

import com.linkcircle.boss.framework.mybatis.core.mapper.BaseMapperX;
import com.linkcircle.boss.module.billing.web.detail.rate.cost.model.entity.CostFixedRateCycleStatusDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-06-25 10:31
 * @description 成本-固定费率周期状态表数据访问层
 */
@Mapper
public interface CostFixedRateCycleStatusMapper extends BaseMapperX<CostFixedRateCycleStatusDO> {

    /**
     * 累加总用量
     */
    int updateTotalUsageByCondition(@Param("serviceId") Long serviceId,
                                    @Param("purchaseId") Long purchaseId,
                                    @Param("billingCycle") String billingCycle,
                                    @Param("usage") BigDecimal usage,
                                    @Param("nowTime") Long nowTime);

}
