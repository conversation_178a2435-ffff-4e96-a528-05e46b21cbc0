package com.linkcircle.boss.module.billing.web.detail.rate.income.service;

import com.linkcircle.boss.module.billing.web.data.model.vo.CyclePeriodResultVO;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-08-05 16:21
 * @description 费率用量缓存服务接口
 */
public interface RateUsageCacheService {

    /**
     * 更新套餐费率用量缓存
     *
     * @param cyclePeriodResult 周期结果
     * @param usage            用量
     * @param usageUnit        用量单位
     * @return 是否成功
     */
    boolean updatePackageRateUsageCache(CyclePeriodResultVO cyclePeriodResult, BigDecimal usage, String usageUnit);

    /**
     * 更新按量费率用量缓存
     *
     * @param cyclePeriodResult 周期结果
     * @param usage            用量
     * @param usageUnit        用量单位
     * @return 是否成功
     */
    boolean updateUsageRateUsageCache(CyclePeriodResultVO cyclePeriodResult, BigDecimal usage, String usageUnit);

    /**
     * 从数据库加载用量数据到缓存
     *
     * @param rateType       费率类型
     * @param serviceId      服务ID
     * @param subscriptionId 订阅ID
     * @param billingCycle   计费周期
     * @return 是否成功
     */
    boolean loadUsageFromDatabase(String rateType, Long serviceId, Long subscriptionId, String billingCycle);

    /**
     * 获取缓存中的总用量
     *
     * @param rateType       费率类型
     * @param serviceId      服务ID
     * @param subscriptionId 订阅ID
     * @param billingCycle   计费周期
     * @return 总用量，如果不存在返回null
     */
    BigDecimal getCachedTotalUsage(String rateType, Long serviceId, Long subscriptionId, String billingCycle);
}
