package com.linkcircle.boss.module.charge.fee.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "unified-recharge")
@Data
public class UnifiedRechargeConfig {
    private String url;
    // 固定支付渠道
    private String wayCode;
    // 固定版本号
    private String version;
    // 固定服务号
    private String isvId;
    // 支付结果异步回调URL 统一调用fee
    private String notifyUrl;
    // 固定币种
    private String currency;
    // 默认商品标题
    private String subject;
    private String body;
    // 固定签名
    private String signKey;
    // 默认查询接口 用于超市确认统一支付订单状态
    private String queryUrl;
}
