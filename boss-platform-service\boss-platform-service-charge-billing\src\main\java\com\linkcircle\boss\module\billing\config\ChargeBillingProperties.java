package com.linkcircle.boss.module.billing.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * <AUTHOR>
 * @date 2025-07-28 14:47
 * @description 计费配置类
 */
@Data
@Component
@ConfigurationProperties(prefix = "boss.billing")
public class ChargeBillingProperties {

    private Boolean useLocalCache = false;

    private String defaultTimezone = "UTC";

    /**
     * Redis缓存相关配置
     */
    private Cache cache = new Cache();

    /**
     * ZSet分片相关配置
     */
    private Shard shard = new Shard();

    /**
     * 同步任务相关配置
     */
    private Sync sync = new Sync();

    @Data
    public static class Cache {
        /**
         * 缓存TTL基础时间（天）
         * 实际TTL = 计费周期时长 + bufferDays
         */
        private Integer bufferDays = 30;

        /**
         * 查询缓存TTL（小时）
         */
        private Integer queryTtlHours = 24;

        /**
         * 是否启用查询缓存
         */
        private Boolean enableQueryCache = true;
    }

    @Data
    public static class Shard {
        /**
         * ZSet分片数量
         * 建议值：50-200，根据数据量调整
         */
        private Integer count = 100;

        /**
         * 每个分片最大元素数量
         * 超过此数量时会自动清理最旧的数据
         */
        private Integer maxSize = 1000;

        /**
         * 过期数据清理时间阈值（小时）
         * 清理指定小时数之前的数据
         */
        private Integer expireHours = 1;
    }

    @Data
    public static class Sync {
        /**
         * 批量同步批次大小
         */
        private Integer batchSize = 200;

        /**
         * 单轮最大处理数量
         * 避免单次处理时间过长
         */
        private Integer maxProcessPerRound = 2000;

        /**
         * 同步任务执行间隔（分钟）
         */
        private Integer intervalMinutes = 2;

        /**
         * 监控任务执行间隔（分钟）
         */
        private Integer monitorIntervalMinutes = 10;

        /**
         * 过载分片阈值（百分比）
         * 分片容量超过此阈值时发出告警
         */
        private Double overloadThreshold = 0.8;
    }
}
