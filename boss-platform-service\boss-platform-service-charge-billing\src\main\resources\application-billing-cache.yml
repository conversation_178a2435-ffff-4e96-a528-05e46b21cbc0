# 计费模块Redis缓存配置示例
boss:
  billing:
    # 是否使用本地缓存
    use-local-cache: false
    # 默认时区
    default-timezone: "UTC"
    
    # Redis缓存相关配置
    cache:
      # 缓存TTL基础时间（天）
      # 实际TTL = 计费周期时长 + bufferDays
      buffer-days: 30
      # 查询缓存TTL（小时）
      query-ttl-hours: 24
      # 是否启用查询缓存
      enable-query-cache: true
    
    # ZSet分片相关配置
    shard:
      # ZSet分片数量
      # 建议值：50-200，根据数据量调整
      # 分片数量 = 预期最大并发数 / 每分片最大容量
      count: 100
      # 每个分片最大元素数量
      # 超过此数量时会自动清理最旧的数据
      max-size: 1000
      # 过期数据清理时间阈值（小时）
      # 清理指定小时数之前的数据
      expire-hours: 1
    
    # 同步任务相关配置
    sync:
      # 批量同步批次大小
      # 建议值：100-500，根据数据库性能调整
      batch-size: 200
      # 单轮最大处理数量
      # 避免单次处理时间过长
      max-process-per-round: 2000
      # 同步任务执行间隔（分钟）
      # 建议值：1-5分钟
      interval-minutes: 2
      # 监控任务执行间隔（分钟）
      # 建议值：5-30分钟
      monitor-interval-minutes: 10
      # 过载分片阈值（百分比）
      # 分片容量超过此阈值时发出告警
      overload-threshold: 0.8

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev
boss:
  billing:
    cache:
      buffer-days: 7  # 开发环境缓存时间短一些
      query-ttl-hours: 2
    shard:
      count: 10       # 开发环境分片数量少一些
      max-size: 100
    sync:
      batch-size: 50
      max-process-per-round: 500

---
# 测试环境配置
spring:
  config:
    activate:
      on-profile: test
boss:
  billing:
    cache:
      buffer-days: 15
      query-ttl-hours: 12
    shard:
      count: 50
      max-size: 500
    sync:
      batch-size: 100
      max-process-per-round: 1000

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod
boss:
  billing:
    cache:
      buffer-days: 30
      query-ttl-hours: 24
    shard:
      count: 100
      max-size: 1000
    sync:
      batch-size: 200
      max-process-per-round: 2000
      interval-minutes: 2
      monitor-interval-minutes: 10
