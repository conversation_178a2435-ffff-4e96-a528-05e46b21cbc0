package com.linkcircle.boss.module.billing.web.detail.cost.service.calculation.strategy.impl;

import cn.hutool.core.lang.Pair;
import com.linkcircle.boss.framework.common.exception.util.ServiceExceptionUtil;
import com.linkcircle.boss.module.billing.api.rate.model.dto.PackageRateConfigDTO;
import com.linkcircle.boss.module.billing.constants.ErrorCodeConstants;
import com.linkcircle.boss.module.billing.constants.RateTypeConstant;
import com.linkcircle.boss.module.billing.enums.CostRateTypeEnum;
import com.linkcircle.boss.module.billing.enums.OriginalPriceRateTypeEnum;
import com.linkcircle.boss.module.billing.web.data.model.vo.CyclePeriodResultVO;
import com.linkcircle.boss.module.billing.web.data.service.CyclePeriodCalculateService;
import com.linkcircle.boss.module.billing.web.detail.cost.model.dto.ResourceSubscriptionInfoDTO;
import com.linkcircle.boss.module.billing.web.detail.cost.service.calculation.strategy.AbstractCostRateChargeStrategy;
import com.linkcircle.boss.module.billing.web.detail.cost.service.calculation.strategy.context.CostRateChargeRequest;
import com.linkcircle.boss.module.billing.web.detail.cost.service.calculation.strategy.context.CostRateChargeResponse;
import com.linkcircle.boss.module.billing.web.detail.rate.cost.model.vo.RateUsageVO;
import com.linkcircle.boss.module.billing.web.detail.rate.cost.service.CostRateUsageManageService;
import com.linkcircle.boss.module.billing.web.detail.rate.strategy.context.OriginalPriceCalculateRequest;
import com.linkcircle.boss.module.billing.web.detail.rate.strategy.context.OriginalPriceCalculateResponse;
import com.linkcircle.boss.module.crm.api.supplier.account.vo.SupplierAccountVO;
import com.linkcircle.boss.module.crm.api.supplier.purchase.vo.ResourcePurchaseVO;
import com.linkcircle.boss.module.crm.enums.ChargeRateTypeEnum;
import io.github.kk01001.design.pattern.strategy.IStrategy;
import io.github.kk01001.design.pattern.strategy.StrategyFactory;
import io.github.kk01001.design.pattern.strategy.annotation.Strategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-06-24 16:10
 * @description 成本套餐费率计费策略
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Strategy(strategyEnum = CostRateTypeEnum.class, strategyType = RateTypeConstant.PACKAGE)
public class CostPackageRateChargeStrategy extends AbstractCostRateChargeStrategy implements IStrategy<CostRateChargeRequest, CostRateChargeResponse> {

    private final CyclePeriodCalculateService cyclePeriodCalculateService;
    private final CostRateUsageManageService costRateUsageManageService;
    private final StrategyFactory strategyFactory;

    @Override
    protected CyclePeriodCalculateService getCyclePeriodCalculateService() {
        return cyclePeriodCalculateService;
    }

    @Override
    public CostRateChargeResponse execute(CostRateChargeRequest request) {
        log.info("开始执行成本套餐费率计费策略");

        // 1. 获取基础数据
        SupplierAccountVO supplierAccountVO = request.getSupplierAccountVO();
        ResourceSubscriptionInfoDTO resourceInfo = request.getResourceSubscriptionInfoDTO();
        ResourcePurchaseVO purchaseVO = resourceInfo.getPurchaseVO();
        ResourcePurchaseVO.Detail detail = resourceInfo.getDetail();
        BigDecimal currentUsage = request.getUsage();
        String usageUnit = request.getUsageUnit();
        String accountCurrency = request.getSupplierAccountVO().getCurrency();

        // 2. 解析套餐费率配置
        Pair<PackageRateConfigDTO, PackageRateConfigDTO.PackageConfigDTO> packageRateConfigPair
                = getPackageRateConfigDTO(detail.getCurrencyPriceJson(), accountCurrency);
        PackageRateConfigDTO rateConfig = packageRateConfigPair.getKey();
        PackageRateConfigDTO.PackageConfigDTO packageConfig = packageRateConfigPair.getValue();

        // 4. 计算计费周期
        Long businessTime = request.getBusinessTime();
        CyclePeriodResultVO cyclePeriodResultVO = getBillingCycle(resourceInfo, supplierAccountVO, businessTime);

        // 5. 获取当前周期累计用量
        RateUsageVO rateUsageVO = costRateUsageManageService.getRateUsage(ChargeRateTypeEnum.PACKAGE, cyclePeriodResultVO);
        BigDecimal previousUsage = rateUsageVO.getUsageCount();
        BigDecimal totalUsageWithCurrent = previousUsage.add(currentUsage);

        // 6. 计算原价
        OriginalPriceCalculateResponse calculateResponse = calculateOriginalPriceByStrategy(purchaseVO, totalUsageWithCurrent,
                previousUsage, currentUsage, usageUnit, rateConfig, packageConfig, detail.getPaymentOptions());

        // 7. db更新费率用量
        costRateUsageManageService.updateRateUsage(cyclePeriodResultVO, currentUsage, request.getUsageUnit(), rateUsageVO.getExist());

        // 8. 构建响应结果
        CostRateChargeResponse response = CostRateChargeResponse.success();
        response.setCurrency(accountCurrency);
        response.setUsage(currentUsage);
        response.setUsageUnit(request.getUsageUnit());
        response.setCyclePeriodResultVO(cyclePeriodResultVO);
        response.setRateConfig(packageConfig);
        convertChargeResponse(response, calculateResponse);
        return response;
    }

    /**
     * 通过策略计算套餐费率目录价
     *
     * @param totalUsageWithCurrent 包含本次用量的总累计用量
     * @param previousUsage         之前的累计用量
     * @param currentUsage          本次用量
     * @param rateConfig            套餐费率配置
     * @param packageConfig         套餐配置
     * @param paymentOptions        支付方式 0-现金 1-积分
     * @return 本次计费金额
     */
    private OriginalPriceCalculateResponse calculateOriginalPriceByStrategy(ResourcePurchaseVO purchaseVO,
                                                                            BigDecimal totalUsageWithCurrent,
                                                                            BigDecimal previousUsage,
                                                                            BigDecimal currentUsage,
                                                                            String usageUnit,
                                                                            PackageRateConfigDTO rateConfig,
                                                                            PackageRateConfigDTO.PackageConfigDTO packageConfig,
                                                                            Integer paymentOptions) {
        // 构建原价计算请求
        OriginalPriceCalculateRequest request = new OriginalPriceCalculateRequest();
        request.setTotalUsageWithCurrent(totalUsageWithCurrent);
        request.setPreviousUsage(previousUsage);
        request.setCurrentUsage(currentUsage);
        request.setCurrentUsageUnit(usageUnit);
        request.setRateConfig(rateConfig);
        request.setPackageConfig(packageConfig);
        request.setPaymentOptions(paymentOptions);
        request.setCalculateTaxEnabled(purchaseVO.getIsTaxInclusive());
        request.setTaxRate(purchaseVO.getRate());

        // 调用原价计算策略
        OriginalPriceCalculateResponse response = strategyFactory.execute(OriginalPriceRateTypeEnum.class,
                OriginalPriceRateTypeEnum.PACKAGE.name(), request);
        if (response.getSuccess()) {
            return response;
        }
        log.error("原价计算失败: {}", response.getErrorMessage());
        throw ServiceExceptionUtil.exception(ErrorCodeConstants.BILLING_CALCULATION_FAILED, response.getErrorMessage());
    }
}
