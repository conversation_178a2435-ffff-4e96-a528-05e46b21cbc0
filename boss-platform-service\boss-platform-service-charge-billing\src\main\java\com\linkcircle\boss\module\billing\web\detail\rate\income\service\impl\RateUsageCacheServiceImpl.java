package com.linkcircle.boss.module.billing.web.detail.rate.income.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.linkcircle.boss.framework.common.util.cache.ChargeCacheUtils;
import com.linkcircle.boss.module.billing.web.data.model.vo.CyclePeriodResultVO;
import com.linkcircle.boss.module.billing.web.detail.rate.income.mapper.IncomePackageRateUsageMapper;
import com.linkcircle.boss.module.billing.web.detail.rate.income.mapper.IncomeUsageRateUsageMapper;
import com.linkcircle.boss.module.billing.web.detail.rate.income.model.entity.IncomePackageRateUsageDO;
import com.linkcircle.boss.module.billing.web.detail.rate.income.model.entity.IncomeUsageRateUsageDO;
import com.linkcircle.boss.module.billing.web.detail.rate.income.service.RateUsageCacheService;
import io.github.kk01001.redis.RedissonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-08-05 16:21
 * @description 费率用量缓存服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RateUsageCacheServiceImpl implements RateUsageCacheService {

    private final RedissonUtil redissonUtil;
    private final IncomePackageRateUsageMapper incomePackageRateUsageMapper;
    private final IncomeUsageRateUsageMapper incomeUsageRateUsageMapper;
    private final RateUsageSyncTracker syncTracker;

    private static final String RATE_TYPE_PACKAGE = "package";
    private static final String RATE_TYPE_USAGE = "usage";
    
    // Hash字段名常量
    private static final String FIELD_TOTAL_USAGE = "total_usage";
    private static final String FIELD_BILLING_TIME = "billing_time";
    private static final String FIELD_USAGE_UNIT = "usage_unit";
    private static final String FIELD_CURRENCY = "currency";

    @Override
    public boolean updatePackageRateUsageCache(CyclePeriodResultVO cyclePeriodResult, BigDecimal usage, String usageUnit) {
        return updateRateUsageCache(RATE_TYPE_PACKAGE, cyclePeriodResult, usage, usageUnit);
    }

    @Override
    public boolean updateUsageRateUsageCache(CyclePeriodResultVO cyclePeriodResult, BigDecimal usage, String usageUnit) {
        return updateRateUsageCache(RATE_TYPE_USAGE, cyclePeriodResult, usage, usageUnit);
    }

    @Override
    public boolean loadUsageFromDatabase(String rateType, Long serviceId, Long subscriptionId, String billingCycle) {
        try {
            String cacheKey = ChargeCacheUtils.getRateUsageCacheKey(rateType, serviceId, subscriptionId, billingCycle);
            
            if (RATE_TYPE_PACKAGE.equals(rateType)) {
                return loadPackageUsageFromDatabase(cacheKey, serviceId, subscriptionId, billingCycle);
            } else if (RATE_TYPE_USAGE.equals(rateType)) {
                return loadUsageUsageFromDatabase(cacheKey, serviceId, subscriptionId, billingCycle);
            }
            
            return false;
        } catch (Exception e) {
            log.error("从数据库加载用量数据到缓存失败, rateType: {}, serviceId: {}, subscriptionId: {}, billingCycle: {}", 
                    rateType, serviceId, subscriptionId, billingCycle, e);
            return false;
        }
    }

    @Override
    public BigDecimal getCachedTotalUsage(String rateType, Long serviceId, Long subscriptionId, String billingCycle) {
        try {
            String cacheKey = ChargeCacheUtils.getRateUsageCacheKey(rateType, serviceId, subscriptionId, billingCycle);
            String totalUsageStr = redissonUtil.hget(cacheKey, FIELD_TOTAL_USAGE);
            
            if (StrUtil.isNotBlank(totalUsageStr)) {
                return new BigDecimal(totalUsageStr);
            }
            
            return null;
        } catch (Exception e) {
            log.error("获取缓存中的总用量失败, rateType: {}, serviceId: {}, subscriptionId: {}, billingCycle: {}", 
                    rateType, serviceId, subscriptionId, billingCycle, e);
            return null;
        }
    }

    /**
     * 通用的费率用量缓存更新方法
     */
    private boolean updateRateUsageCache(String rateType, CyclePeriodResultVO cyclePeriodResult, 
                                       BigDecimal usage, String usageUnit) {
        try {
            Long serviceId = cyclePeriodResult.getServiceId();
            Long subscriptionId = cyclePeriodResult.getSubscriptionId();
            String billingCycle = cyclePeriodResult.getBillingCycle();
            String currency = cyclePeriodResult.getCurrency();
            long nowTime = System.currentTimeMillis();

            String cacheKey = ChargeCacheUtils.getRateUsageCacheKey(rateType, serviceId, subscriptionId, billingCycle);
            
            // 检查缓存是否存在，如果不存在则从数据库加载
            if (!redissonUtil.exists(cacheKey)) {
                loadUsageFromDatabase(rateType, serviceId, subscriptionId, billingCycle);
            }

            // 使用Redis Hash原子操作更新用量
            Map<String, Object> hashMap = new HashMap<>();
            hashMap.put(FIELD_BILLING_TIME, String.valueOf(nowTime));
            hashMap.put(FIELD_USAGE_UNIT, usageUnit);
            hashMap.put(FIELD_CURRENCY, currency);

            // 原子递增总用量
            redissonUtil.hincrby(cacheKey, FIELD_TOTAL_USAGE, usage);
            redissonUtil.hmset(cacheKey, hashMap);

            // 设置TTL：周期结束时间 + 30天缓冲期
            long ttlMillis = cyclePeriodResult.getCycleEndTime() - cyclePeriodResult.getCycleStartTime() +
                           Duration.ofDays(30).toMillis();
            redissonUtil.expire(cacheKey, Duration.ofMillis(ttlMillis));

            // 添加到待同步Set中
            syncTracker.addPendingSync(cacheKey);

            log.info("更新{}费率用量缓存成功, serviceId: {}, subscriptionId: {}, billingCycle: {}, usage: {}",
                    rateType, serviceId, subscriptionId, billingCycle, usage);
            return true;
            
        } catch (Exception e) {
            log.error("更新{}费率用量缓存失败, cyclePeriodResult: {}, usage: {}, usageUnit: {}", 
                    rateType, cyclePeriodResult, usage, usageUnit, e);
            return false;
        }
    }

    /**
     * 从数据库加载套餐费率用量数据到缓存
     */
    private boolean loadPackageUsageFromDatabase(String cacheKey, Long serviceId, Long subscriptionId, String billingCycle) {
        LambdaQueryWrapper<IncomePackageRateUsageDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IncomePackageRateUsageDO::getServiceId, serviceId)
                .eq(IncomePackageRateUsageDO::getSubscriptionId, subscriptionId)
                .eq(IncomePackageRateUsageDO::getBillingCycle, billingCycle);

        IncomePackageRateUsageDO usageDO = incomePackageRateUsageMapper.selectOne(queryWrapper);
        
        if (Objects.nonNull(usageDO)) {
            Map<String, Object> hashMap = new HashMap<>();
            hashMap.put(FIELD_TOTAL_USAGE, usageDO.getTotalUsage().toString());
            hashMap.put(FIELD_BILLING_TIME, String.valueOf(usageDO.getBillingTime()));
            hashMap.put(FIELD_USAGE_UNIT, usageDO.getUsageUnit());
            hashMap.put(FIELD_CURRENCY, usageDO.getCurrency());
            
            redissonUtil.hmset(cacheKey, hashMap);
            log.info("从数据库加载套餐费率用量数据到缓存成功, cacheKey: {}", cacheKey);
            return true;
        } else {
            // 数据库中不存在，设置默认值
            Map<String, Object> hashMap = new HashMap<>();
            hashMap.put(FIELD_TOTAL_USAGE, "0");
            hashMap.put(FIELD_BILLING_TIME, String.valueOf(System.currentTimeMillis()));
            
            redissonUtil.hmset(cacheKey, hashMap);
            log.info("数据库中不存在套餐费率用量数据，设置默认缓存值, cacheKey: {}", cacheKey);
            return true;
        }
    }

    /**
     * 从数据库加载按量费率用量数据到缓存
     */
    private boolean loadUsageUsageFromDatabase(String cacheKey, Long serviceId, Long subscriptionId, String billingCycle) {
        LambdaQueryWrapper<IncomeUsageRateUsageDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IncomeUsageRateUsageDO::getServiceId, serviceId)
                .eq(IncomeUsageRateUsageDO::getSubscriptionId, subscriptionId)
                .eq(IncomeUsageRateUsageDO::getBillingCycle, billingCycle);

        IncomeUsageRateUsageDO usageDO = incomeUsageRateUsageMapper.selectOne(queryWrapper);
        
        if (Objects.nonNull(usageDO)) {
            Map<String, Object> hashMap = new HashMap<>();
            hashMap.put(FIELD_TOTAL_USAGE, usageDO.getTotalUsage().toString());
            hashMap.put(FIELD_BILLING_TIME, String.valueOf(usageDO.getBillingTime()));
            hashMap.put(FIELD_USAGE_UNIT, usageDO.getUsageUnit());
            hashMap.put(FIELD_CURRENCY, usageDO.getCurrency());
            
            redissonUtil.hmset(cacheKey, hashMap);
            log.info("从数据库加载按量费率用量数据到缓存成功, cacheKey: {}", cacheKey);
            return true;
        } else {
            // 数据库中不存在，设置默认值
            Map<String, Object> hashMap = new HashMap<>();
            hashMap.put(FIELD_TOTAL_USAGE, "0");
            hashMap.put(FIELD_BILLING_TIME, String.valueOf(System.currentTimeMillis()));
            
            redissonUtil.hmset(cacheKey, hashMap);
            log.info("数据库中不存在按量费率用量数据，设置默认缓存值, cacheKey: {}", cacheKey);
            return true;
        }
    }
}
