package com.linkcircle.boss.module.charge.fee.web.payment.service;

import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.billing.api.wallet.model.dto.WalletDeductionMqDTO;
import com.linkcircle.boss.module.charge.fee.web.payment.model.dto.WalletBalanceDoMakeupUnPayDTO;
import com.linkcircle.boss.module.charge.fee.web.payment.model.dto.WalletBalanceDoPostpaidUnPayDTO;
import com.linkcircle.boss.module.charge.fee.web.payment.model.dto.WalletBalanceDoPrepaidUnPayDTO;

import java.util.Map;

public interface IUnpayBillSettleService {

    /**
     * 预付费账单结清
     *
     * @param walletBalanceDoPrepaidUnPayDTO
     * @return
     */
    CommonResult<Map<Long, Boolean>> doUnPayPrepaidBill(WalletBalanceDoPrepaidUnPayDTO walletBalanceDoPrepaidUnPayDTO);

    /**
     * 后付费账单结清
     *
     * @param walletBalanceDoPostpaidUnPayDTO
     * @return
     * @throws Exception
     */
    CommonResult<Map<Long, Boolean>> doUnPayPostpaidBill(WalletBalanceDoPostpaidUnPayDTO walletBalanceDoPostpaidUnPayDTO) throws Exception;

    /**
     * 手工账单结清
     *
     * @param walletBalanceDoMakeupUnPayDTO
     * @return
     */
    CommonResult<Map<Long, Boolean>> doUnPayMakeupBill(WalletBalanceDoMakeupUnPayDTO walletBalanceDoMakeupUnPayDTO);

    /**
     * 发起结清
     *
     * @param walletDeductionMqDTO
     * @return
     * @throws Exception
     */
    boolean dealUnSettledBusiness(WalletDeductionMqDTO walletDeductionMqDTO) throws Exception;

}
