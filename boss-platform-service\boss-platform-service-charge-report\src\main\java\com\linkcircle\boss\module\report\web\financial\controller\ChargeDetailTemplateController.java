package com.linkcircle.boss.module.report.web.financial.controller;

/**
 * <AUTHOR>
 * @date 2025/6/12 15:57
 */

import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.framework.permission.PreAuthorize;
import com.linkcircle.boss.module.report.web.financial.model.dto.ChargeDetailTemplatePageQueryVo;
import com.linkcircle.boss.module.report.web.financial.model.dto.ChargeDetailTemplateReqDTO;
import com.linkcircle.boss.module.report.web.financial.model.vo.ChargeDetailTemplateRespVO;
import com.linkcircle.boss.module.report.web.financial.service.ChargeDetailTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.linkcircle.boss.framework.common.model.CommonResult.success;

@Tag(name = "财务-模板明细")
@RestController
@RequestMapping("/financial/charge-template")
@Validated
public class ChargeDetailTemplateController {

    @Autowired
    private ChargeDetailTemplateService chargeDetailTemplateService;

    @PostMapping("/create")
    @Operation(summary = "创建模板")
    @PreAuthorize("@ss.hasPermission('financial:template:create')")
    public CommonResult<String> createTemplate(@Valid @RequestBody final ChargeDetailTemplateReqDTO  templateReqDTO) {
        chargeDetailTemplateService.createTemplate(templateReqDTO);
        return success();
    }

    @PostMapping("/update")
    @Operation(summary = "更新模板")
    @PreAuthorize("@ss.hasPermission('financial:template:update')")
    public CommonResult<String> updateTemplate(@Valid @RequestBody final ChargeDetailTemplateReqDTO  templateReqDTO) {
        chargeDetailTemplateService.updateTemplate(templateReqDTO);
        return success();
    }

    @GetMapping("/delete")
    @Operation(summary = "删除模板")
    @PreAuthorize("@ss.hasPermission('financial:template:delete')")
    @Parameters({
            @Parameter(name = "templateId", description = "模板id", example = "1", required = true)
    })
    public CommonResult<String> deleteTemplate(@RequestParam(required = true,name = "templateId") String templateId) {
        chargeDetailTemplateService.deleteTemplate(templateId);
        return success();
    }


    @GetMapping("/list")
    @Operation(summary = "模板列表查询")
    @PreAuthorize("@ss.hasPermission('financial:template:query')")
    @Parameters({
            @Parameter(name = "templateId", description = "模板id", example = "1", required = false),
            @Parameter(name = "templateName", description = "模板名称", example = "AI智能通信模板", required = false)
    })
    public CommonResult<List<ChargeDetailTemplateRespVO>> listTemplate(@RequestParam(required = false,name = "templateName") String templateName,
                                                                       @RequestParam(required = false,name = "templateId") String templateId) {

        return success(chargeDetailTemplateService.listTemplate(templateName,templateId));
    }

    @GetMapping("/detail")
    @Operation(summary = "模板列表查询")
    @PreAuthorize("@ss.hasPermission('financial:template:detail')")
    @Parameter(name = "templateId", description = "模板id", example = "1", required = true)
    public CommonResult<ChargeDetailTemplateRespVO> detail(@RequestParam(required = true,name = "templateId") String templateId) {
        return success(chargeDetailTemplateService.detail(templateId));
    }


    @PostMapping("/page")
    @Operation(summary = "模板分页查询")
    @PreAuthorize("@ss.hasPermission('financial:template:page')")
    public CommonResult<PageResult<ChargeDetailTemplateRespVO>> listTemplate(@RequestBody @Valid ChargeDetailTemplatePageQueryVo pageQueryVo) {

        return success(chargeDetailTemplateService.pageQuery(pageQueryVo));
    }
}
