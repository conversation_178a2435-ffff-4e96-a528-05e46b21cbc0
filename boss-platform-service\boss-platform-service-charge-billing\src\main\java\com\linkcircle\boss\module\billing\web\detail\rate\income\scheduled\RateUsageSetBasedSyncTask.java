package com.linkcircle.boss.module.billing.web.detail.rate.income.scheduled;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.linkcircle.boss.framework.web.context.EnableLoginContext;
import com.linkcircle.boss.module.billing.web.detail.rate.income.mapper.IncomePackageRateUsageMapper;
import com.linkcircle.boss.module.billing.web.detail.rate.income.mapper.IncomeUsageRateUsageMapper;
import com.linkcircle.boss.module.billing.web.detail.rate.income.model.entity.IncomePackageRateUsageDO;
import com.linkcircle.boss.module.billing.web.detail.rate.income.model.entity.IncomeUsageRateUsageDO;
import com.linkcircle.boss.module.billing.web.detail.rate.income.service.impl.RateUsageSyncTracker;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.github.kk01001.redis.RedissonUtil;
import io.github.kk01001.util.TraceIdUtil;
import io.github.kk01001.xxljob.annotations.XxlJobRegister;
import io.github.kk01001.xxljob.enums.ExecutorRouteStrategyEnum;
import io.github.kk01001.xxljob.enums.MisfireStrategyEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025-08-05 16:21
 * @description 基于Redis Set的费率用量批量同步任务（推荐方案）
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RateUsageSetBasedSyncTask {

    private final RedissonUtil redissonUtil;
    private final RateUsageSyncTracker syncTracker;
    private final IncomePackageRateUsageMapper incomePackageRateUsageMapper;
    private final IncomeUsageRateUsageMapper incomeUsageRateUsageMapper;

    private static final int BATCH_SIZE = 100; // 每批处理100个key

    @XxlJob("rateUsageSetBasedSyncHandler")
    @XxlJobRegister(
            cron = "0 */2 * * * ?", // 每2分钟执行一次
            jobDesc = "基于Set的费率用量批量同步任务",
            author = "linshiqiang",
            triggerStatus = 1,
            executorRouteStrategy = ExecutorRouteStrategyEnum.ROUND,
            misfireStrategy = MisfireStrategyEnum.DO_NOTHING
    )
    public void rateUsageSetBasedSyncHandler() {
        String traceId = TraceIdUtil.generateTraceId();
        TraceIdUtil.setTraceId(traceId);
        
        try {
            log.info("开始执行基于Set的费率用量批量同步任务");
            
            // 设置登录用户上下文
            EnableLoginContext.setLoginUserId(1L);
            EnableLoginContext.setLoginUserType(1);

            // 先恢复上次可能未完成的处理
            syncTracker.recoverProcessingKeys();
            
            // 获取待同步数量
            long pendingCount = syncTracker.getPendingCount();
            if (pendingCount == 0) {
                log.debug("没有待同步的费率用量数据");
                return;
            }
            
            log.info("开始批量同步费率用量，待同步数量: {}", pendingCount);
            
            int totalProcessed = 0;
            int successCount = 0;
            int failCount = 0;
            
            // 分批处理
            while (true) {
                Set<String> keys = syncTracker.getAndRemovePendingKeys(BATCH_SIZE);
                if (CollUtil.isEmpty(keys)) {
                    break;
                }
                
                totalProcessed += keys.size();
                
                for (String key : keys) {
                    try {
                        if (syncSingleRateUsage(key)) {
                            successCount++;
                            syncTracker.markSyncCompleted(key);
                        } else {
                            failCount++;
                            // 同步失败的key重新加入待同步Set
                            syncTracker.addPendingSync(key);
                        }
                    } catch (Exception e) {
                        failCount++;
                        log.error("同步单个费率用量失败, key: {}", key, e);
                        // 异常的key重新加入待同步Set
                        syncTracker.addPendingSync(key);
                    }
                }
                
                // 避免长时间占用，每批处理后短暂休息
                if (totalProcessed >= 1000) {
                    log.info("本次已处理{}个key，暂停处理，等待下次调度", totalProcessed);
                    break;
                }
            }
            
            log.info("基于Set的费率用量批量同步完成，处理: {}, 成功: {}, 失败: {}, 剩余待同步: {}", 
                    totalProcessed, successCount, failCount, syncTracker.getPendingCount());
            
        } catch (Exception e) {
            log.error("基于Set的费率用量批量同步任务执行异常", e);
        } finally {
            // 清理上下文
            EnableLoginContext.clear();
            TraceIdUtil.clear();
        }
    }

    /**
     * 同步单个费率用量
     */
    private boolean syncSingleRateUsage(String cacheKey) {
        try {
            // 解析key获取费率类型
            String[] parts = cacheKey.split(":");
            if (parts.length < 6) {
                log.warn("费率用量缓存key格式不正确: {}", cacheKey);
                return false;
            }
            
            String rateType = parts[4]; // billing:usage:{rateType}:...
            
            if ("package".equals(rateType)) {
                return syncPackageRateUsage(cacheKey, parts);
            } else if ("usage".equals(rateType)) {
                return syncUsageRateUsage(cacheKey, parts);
            } else {
                log.warn("不支持的费率类型: {}, key: {}", rateType, cacheKey);
                return false;
            }
            
        } catch (Exception e) {
            log.error("同步单个费率用量异常, cacheKey: {}", cacheKey, e);
            return false;
        }
    }

    /**
     * 同步套餐费率用量
     */
    private boolean syncPackageRateUsage(String cacheKey, String[] keyParts) {
        try {
            if (keyParts.length != 8) {
                log.warn("套餐费率用量缓存key格式不正确: {}", cacheKey);
                return false;
            }
            
            Long serviceId = Long.valueOf(keyParts[5]);
            Long subscriptionId = Long.valueOf(keyParts[6]);
            String billingCycle = keyParts[7];
            
            // 获取缓存数据
            String totalUsageStr = redissonUtil.hget(cacheKey, "total_usage");
            String billingTimeStr = redissonUtil.hget(cacheKey, "billing_time");
            String usageUnit = redissonUtil.hget(cacheKey, "usage_unit");
            String currency = redissonUtil.hget(cacheKey, "currency");
            
            if (StrUtil.isBlank(totalUsageStr)) {
                log.warn("套餐费率用量缓存数据不完整: {}", cacheKey);
                return false;
            }
            
            BigDecimal totalUsage = new BigDecimal(totalUsageStr);
            Long billingTime = StrUtil.isNotBlank(billingTimeStr) ? Long.valueOf(billingTimeStr) : System.currentTimeMillis();
            
            // 先尝试更新
            int updateCount = incomePackageRateUsageMapper.updateTotalUsageByCondition(serviceId, subscriptionId, billingCycle, totalUsage, billingTime);
            
            if (updateCount > 0) {
                log.debug("更新套餐费率用量数据库记录成功: serviceId={}, subscriptionId={}, billingCycle={}, totalUsage={}", 
                        serviceId, subscriptionId, billingCycle, totalUsage);
                return true;
            } else {
                // 更新失败，可能记录不存在，这里可以选择插入新记录
                log.debug("套餐费率用量数据库记录不存在，跳过同步: serviceId={}, subscriptionId={}, billingCycle={}", 
                        serviceId, subscriptionId, billingCycle);
                return true; // 返回true避免重复处理
            }
            
        } catch (Exception e) {
            log.error("同步套餐费率用量异常, cacheKey: {}", cacheKey, e);
            return false;
        }
    }

    /**
     * 同步按量费率用量
     */
    private boolean syncUsageRateUsage(String cacheKey, String[] keyParts) {
        try {
            if (keyParts.length != 8) {
                log.warn("按量费率用量缓存key格式不正确: {}", cacheKey);
                return false;
            }
            
            Long serviceId = Long.valueOf(keyParts[5]);
            Long subscriptionId = Long.valueOf(keyParts[6]);
            String billingCycle = keyParts[7];
            
            // 获取缓存数据
            String totalUsageStr = redissonUtil.hget(cacheKey, "total_usage");
            String billingTimeStr = redissonUtil.hget(cacheKey, "billing_time");
            String usageUnit = redissonUtil.hget(cacheKey, "usage_unit");
            String currency = redissonUtil.hget(cacheKey, "currency");
            
            if (StrUtil.isBlank(totalUsageStr)) {
                log.warn("按量费率用量缓存数据不完整: {}", cacheKey);
                return false;
            }
            
            BigDecimal totalUsage = new BigDecimal(totalUsageStr);
            Long billingTime = StrUtil.isNotBlank(billingTimeStr) ? Long.valueOf(billingTimeStr) : System.currentTimeMillis();
            
            // 先尝试更新
            int updateCount = incomeUsageRateUsageMapper.updateTotalUsageByCondition(serviceId, subscriptionId, billingCycle, totalUsage, billingTime);
            
            if (updateCount > 0) {
                log.debug("更新按量费率用量数据库记录成功: serviceId={}, subscriptionId={}, billingCycle={}, totalUsage={}", 
                        serviceId, subscriptionId, billingCycle, totalUsage);
                return true;
            } else {
                // 更新失败，可能记录不存在，这里可以选择插入新记录
                log.debug("按量费率用量数据库记录不存在，跳过同步: serviceId={}, subscriptionId={}, billingCycle={}", 
                        serviceId, subscriptionId, billingCycle);
                return true; // 返回true避免重复处理
            }
            
        } catch (Exception e) {
            log.error("同步按量费率用量异常, cacheKey: {}", cacheKey, e);
            return false;
        }
    }
}
