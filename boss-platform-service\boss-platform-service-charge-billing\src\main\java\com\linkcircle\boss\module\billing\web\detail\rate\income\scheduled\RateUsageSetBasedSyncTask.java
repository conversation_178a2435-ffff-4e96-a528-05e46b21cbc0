package com.linkcircle.boss.module.billing.web.detail.rate.income.scheduled;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.linkcircle.boss.framework.web.context.EnableLoginContext;
import com.linkcircle.boss.module.billing.web.detail.rate.common.service.UnifiedRateUsageDbSyncService;
import com.linkcircle.boss.module.billing.web.detail.rate.income.service.impl.RateUsageSyncTracker;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.github.kk01001.redis.RedissonUtil;
import io.github.kk01001.util.TraceIdUtil;
import io.github.kk01001.xxljob.annotations.XxlJobRegister;
import io.github.kk01001.xxljob.enums.ExecutorRouteStrategyEnum;
import io.github.kk01001.xxljob.enums.MisfireStrategyEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025-08-05 16:21
 * @description 基于Redis ZSet分片的费率用量批量同步任务（支持收入和成本）
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RateUsageSetBasedSyncTask {

    private final RedissonUtil redissonUtil;
    private final RateUsageSyncTracker syncTracker;
    private final UnifiedRateUsageDbSyncService unifiedDbSyncService;

    private static final int BATCH_SIZE = 200; // 每批处理200个key
    private static final int MAX_PROCESS_PER_ROUND = 2000; // 每轮最多处理2000个key

    @XxlJob("rateUsageZSetBasedSyncHandler")
    @XxlJobRegister(
            cron = "0 */2 * * * ?", // 每2分钟执行一次
            jobDesc = "基于ZSet分片的费率用量批量同步任务",
            author = "linshiqiang",
            triggerStatus = 1,
            executorRouteStrategy = ExecutorRouteStrategyEnum.ROUND,
            misfireStrategy = MisfireStrategyEnum.DO_NOTHING
    )
    public void rateUsageZSetBasedSyncHandler() {
        String traceId = TraceIdUtil.generateTraceId();
        TraceIdUtil.setTraceId(traceId);

        try {
            log.info("开始执行基于ZSet分片的费率用量批量同步任务");

            // 设置登录用户上下文
            EnableLoginContext.setLoginUserId(1L);
            EnableLoginContext.setLoginUserType(1);

            // 清理过期的key（1小时前的数据）
            long oneHourAgo = System.currentTimeMillis() - 3600000;
            long cleanedCount = syncTracker.cleanupExpiredKeys(oneHourAgo);
            if (cleanedCount > 0) {
                log.info("清理过期key数量: {}", cleanedCount);
            }

            // 获取待同步数量
            long pendingCount = syncTracker.getPendingCount();
            if (pendingCount == 0) {
                log.debug("没有待同步的费率用量数据");
                return;
            }

            log.info("开始批量同步费率用量（收入+成本），待同步数量: {}, 分片数量: {}", pendingCount, syncTracker.getShardCount());

            int totalProcessed = 0;
            int successCount = 0;
            int failCount = 0;

            // 轮询所有分片进行处理
            for (int shardIndex = 0; shardIndex < syncTracker.getShardCount() && totalProcessed < MAX_PROCESS_PER_ROUND; shardIndex++) {
                long shardPendingCount = syncTracker.getShardPendingCount(shardIndex);
                if (shardPendingCount == 0) {
                    continue;
                }

                log.debug("处理分片{}, 待同步数量: {}", shardIndex, shardPendingCount);

                // 从当前分片获取一批数据
                Set<String> keys = syncTracker.getAndRemovePendingKeys(shardIndex, BATCH_SIZE);
                if (CollUtil.isEmpty(keys)) {
                    continue;
                }

                totalProcessed += keys.size();

                for (String key : keys) {
                    try {
                        if (unifiedDbSyncService.processDbSyncFromCacheKey(key)) {
                            successCount++;
                            syncTracker.markSyncCompleted(key);
                        } else {
                            failCount++;
                            // 同步失败的key重新加入（会更新时间戳）
                            syncTracker.reAddFailedKey(key);
                        }
                    } catch (Exception e) {
                        failCount++;
                        log.error("同步单个费率用量失败, key: {}", key, e);
                        // 异常的key重新加入
                        syncTracker.reAddFailedKey(key);
                    }
                }

                // 避免单次处理过多
                if (totalProcessed >= MAX_PROCESS_PER_ROUND) {
                    log.info("本次已处理{}个key，达到单轮限制，等待下次调度", totalProcessed);
                    break;
                }
            }

            log.info("基于ZSet分片的费率用量批量同步完成，处理: {}, 成功: {}, 失败: {}, 剩余待同步: {}",
                    totalProcessed, successCount, failCount, syncTracker.getPendingCount());

        } catch (Exception e) {
            log.error("基于ZSet分片的费率用量批量同步任务执行异常", e);
        } finally {
            // 清理上下文
            EnableLoginContext.clear();
            TraceIdUtil.clear();
        }
    }


}
