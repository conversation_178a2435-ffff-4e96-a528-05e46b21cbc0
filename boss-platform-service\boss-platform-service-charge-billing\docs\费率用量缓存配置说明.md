# 费率用量缓存配置说明

## 📋 配置概述

本文档详细说明了费率用量Redis缓存优化方案中的各项配置参数，包括配置项含义、推荐值、调优建议等。

## 🔧 配置结构

```yaml
boss:
  billing:
    cache:      # Redis缓存相关配置
    shard:      # ZSet分片相关配置  
    sync:       # 同步任务相关配置
```

## 📖 详细配置说明

### 1. 缓存配置 (cache)

#### 1.1 buffer-days
- **含义**：缓存TTL缓冲时间（天）
- **计算公式**：实际TTL = 计费周期时长 + buffer-days
- **默认值**：30天
- **推荐值**：
  - 开发环境：7天
  - 测试环境：15天
  - 生产环境：30天
- **调优建议**：
  - 值过小：可能导致缓存过早失效，增加数据库查询
  - 值过大：占用更多Redis内存，但提高缓存命中率

#### 1.2 query-ttl-hours
- **含义**：查询缓存TTL（小时）
- **用途**：查询结果回写到缓存的过期时间
- **默认值**：24小时
- **推荐值**：
  - 高频查询场景：24-48小时
  - 低频查询场景：6-12小时
- **调优建议**：
  - 根据查询频率和数据更新频率调整
  - 查询频率高且数据变化少：可适当延长
  - 数据变化频繁：应适当缩短

#### 1.3 enable-query-cache
- **含义**：是否启用查询缓存
- **默认值**：true
- **使用场景**：
  - true：启用缓存，提高查询性能
  - false：禁用缓存，直接查询数据库（调试时使用）

### 2. 分片配置 (shard)

#### 2.1 count
- **含义**：ZSet分片数量
- **默认值**：100
- **计算公式**：分片数量 = 预期最大并发数 / 每分片最大容量
- **推荐值**：
  - 小规模（<10万key）：50个分片
  - 中规模（10-100万key）：100个分片
  - 大规模（>100万key）：200个分片
- **调优建议**：
  - 分片过少：单个分片压力大，可能成为瓶颈
  - 分片过多：管理开销增加，负载均衡效果下降

#### 2.2 max-size
- **含义**：每个分片最大元素数量
- **默认值**：1000
- **推荐值**：
  - 内存充足：1000-2000
  - 内存紧张：500-1000
- **调优建议**：
  - 超过此数量时会自动清理最旧的数据
  - 值过小：频繁清理，可能丢失待同步数据
  - 值过大：占用更多内存，清理效率下降

#### 2.3 expire-hours
- **含义**：过期数据清理时间阈值（小时）
- **默认值**：1小时
- **推荐值**：
  - 高频更新：1-2小时
  - 低频更新：2-6小时
- **调优建议**：
  - 清理过于频繁的过期数据，避免内存泄漏
  - 值过小：可能清理还未同步的数据
  - 值过大：过期数据占用内存时间过长

### 3. 同步配置 (sync)

#### 3.1 batch-size
- **含义**：批量同步批次大小
- **默认值**：200
- **推荐值**：
  - 数据库性能好：200-500
  - 数据库性能一般：100-200
  - 数据库性能差：50-100
- **调优建议**：
  - 值过小：同步效率低，增加数据库连接开销
  - 值过大：单次事务时间长，可能导致锁等待

#### 3.2 max-process-per-round
- **含义**：单轮最大处理数量
- **默认值**：2000
- **用途**：避免单次处理时间过长
- **推荐值**：
  - 高性能环境：2000-5000
  - 一般性能环境：1000-2000
  - 低性能环境：500-1000

#### 3.3 interval-minutes
- **含义**：同步任务执行间隔（分钟）
- **默认值**：2分钟
- **推荐值**：
  - 实时性要求高：1-2分钟
  - 实时性要求一般：2-5分钟
  - 实时性要求低：5-10分钟
- **调优建议**：
  - 间隔过短：增加系统负载，但数据一致性更好
  - 间隔过长：降低系统负载，但数据延迟增加

#### 3.4 monitor-interval-minutes
- **含义**：监控任务执行间隔（分钟）
- **默认值**：10分钟
- **推荐值**：5-30分钟
- **调优建议**：
  - 根据监控需求调整
  - 生产环境建议5-10分钟
  - 开发环境可适当延长

#### 3.5 overload-threshold
- **含义**：过载分片阈值（百分比）
- **默认值**：0.8（80%）
- **用途**：分片容量超过此阈值时发出告警
- **推荐值**：0.7-0.9
- **调优建议**：
  - 值过低：频繁告警，影响运维效率
  - 值过高：告警不及时，可能影响性能

## 🚀 环境配置建议

### 开发环境
```yaml
boss:
  billing:
    cache:
      buffer-days: 7
      query-ttl-hours: 2
      enable-query-cache: true
    shard:
      count: 10
      max-size: 100
      expire-hours: 1
    sync:
      batch-size: 50
      max-process-per-round: 500
      interval-minutes: 5
      monitor-interval-minutes: 30
```

### 测试环境
```yaml
boss:
  billing:
    cache:
      buffer-days: 15
      query-ttl-hours: 12
      enable-query-cache: true
    shard:
      count: 50
      max-size: 500
      expire-hours: 2
    sync:
      batch-size: 100
      max-process-per-round: 1000
      interval-minutes: 3
      monitor-interval-minutes: 15
```

### 生产环境
```yaml
boss:
  billing:
    cache:
      buffer-days: 30
      query-ttl-hours: 24
      enable-query-cache: true
    shard:
      count: 100
      max-size: 1000
      expire-hours: 1
    sync:
      batch-size: 200
      max-process-per-round: 2000
      interval-minutes: 2
      monitor-interval-minutes: 10
      overload-threshold: 0.8
```

## 📊 性能调优指南

### 1. 内存优化
- **分片数量 × 分片大小 ≤ 可用内存 / 单个元素大小**
- **单个元素大小约280字节**
- **100个分片 × 1000个元素 ≈ 28MB**

### 2. 性能优化
- **批次大小**：根据数据库TPS调整
- **同步间隔**：根据实时性要求调整
- **分片数量**：根据并发量调整

### 3. 监控指标
- **缓存命中率**：> 95%
- **同步成功率**：> 99%
- **平均响应时间**：< 5ms
- **分片负载均衡度**：> 0.7

## ⚠️ 注意事项

1. **配置修改**：
   - 生产环境配置修改需要重启应用
   - 建议在低峰期进行配置调整

2. **内存监控**：
   - 定期监控Redis内存使用情况
   - 避免内存溢出导致系统不稳定

3. **数据一致性**：
   - 配置调整后需要验证数据一致性
   - 建议进行充分的测试验证

4. **性能测试**：
   - 配置调整后进行性能测试
   - 确保满足业务性能要求

---

**文档版本**：v1.0  
**更新时间**：2025-08-05  
**维护人员**：linshiqiang
