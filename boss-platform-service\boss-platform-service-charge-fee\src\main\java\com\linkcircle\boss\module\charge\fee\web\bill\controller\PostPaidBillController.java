package com.linkcircle.boss.module.charge.fee.web.bill.controller;

import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.framework.permission.PreAuthorize;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.BillReqDto;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.BillReqHistoryDto;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.BillSendEmailReqDto;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.calculate.BillFeeCalculateReqDTO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.calculate.BillFeeResponseVO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.history.IncomeBillHistoryVO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.postpaid.PostpaidIncomeProductBillDetailVO;
import com.linkcircle.boss.module.charge.fee.web.bill.service.BillFeeService;
import com.linkcircle.boss.module.charge.fee.web.bill.service.PostpaidService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.linkcircle.boss.framework.common.model.CommonResult.success;

/**
 * <AUTHOR>
 * @date 2025/6/30 17:23
 */
@Tag(name = "账单-后付费账单")
@RestController
@RequestMapping("/customer/postpaid-bill")
@Validated
@RequiredArgsConstructor
public class PostPaidBillController {


    private final PostpaidService postpaidService;
    private final BillFeeService billFeeService;


    @PostMapping("/payOff")
    @Operation(summary = "发起后付费账单结清")
    @PreAuthorize("@ss.hasPermission('customer:prepaid-bill:pay-off')")
    public CommonResult<String> payOffBill(@Valid @RequestBody BillReqDto reqDto) {
        postpaidService.payOffBill(reqDto);
        return success();
    }


    @PostMapping("/make-sure")
    @Operation(summary = "后付费-账单确认")
    @PreAuthorize("@ss.hasPermission('customer:postpaid-bill:make-sure')")
    public CommonResult<BillFeeResponseVO> makeSure(@Valid @RequestBody BillFeeCalculateReqDTO reqDto) {
        return success(billFeeService.makeSureBill(reqDto));
    }


    @PostMapping("/to-draft")
    @Operation(summary = "后付费-账单有确认退回为草稿状态")
    @PreAuthorize("@ss.hasPermission('customer:postpaid-bill:to-draft')")
    public CommonResult<String> toDraft(@Valid @RequestBody BillReqDto reqDto) {
        postpaidService.toDraft(reqDto);
        return success();
    }

    @PostMapping("/history-list")
    @Operation(summary = "后付费-账单历史记录")
    @PreAuthorize("@ss.hasPermission('customer:postpaid-bill:history')")
    public CommonResult<PageResult<IncomeBillHistoryVO<PostpaidIncomeProductBillDetailVO>>> historyBills(@Valid @RequestBody BillReqHistoryDto reqDto) {
        return success(postpaidService.historyBills(reqDto));
    }

    @PostMapping("/delete")
    @Operation(summary = "删除后付费")
    @PreAuthorize("@ss.hasPermission('customer:postpaid-bill:delete')")
    public CommonResult<String> deleteBill(@Valid@RequestBody BillReqDto reqDto) {
        postpaidService.deleteBill(reqDto);
        return success();
    }


    @PostMapping("/sendMail")
    @Operation(summary = "发送邮箱")
    @PreAuthorize("@ss.hasPermission('customer:postpaid-bill:send-email')")
    public CommonResult<Boolean> sendMail(@RequestBody BillSendEmailReqDto dto) {
        Boolean vo = postpaidService.sendMail(dto);
        return CommonResult.success(vo);
    }
}
