<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.linkcircle.boss.module.report.web.bill.mapper.MakeupProductServiceBillMapper">


    <select id="queryByBillIds"
            resultType="com.linkcircle.boss.module.report.web.bill.model.entity.MakeupProductServiceIncomeBillDO">


            select * from makeup_product_service_income_bill where bill_id in
            <foreach collection="billIds" item="billId" separator="," open="(" close=")">
                #{billId}
            </foreach>
            and deleted = 0
            <if test="startTime != null ">
                and bill_date &gt;= #{startTime}
            </if>
            <if test=" endTime != null">
                and bill_date &lt;= #{endTime}
            </if>

    </select>
</mapper>