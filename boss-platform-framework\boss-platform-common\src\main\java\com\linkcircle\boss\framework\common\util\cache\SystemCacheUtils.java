package com.linkcircle.boss.framework.common.util.cache;

import cn.hutool.core.util.StrUtil;
import com.linkcircle.boss.framework.common.constants.SystemCacheConstant;

/**
 * <AUTHOR>
 * @date 2025-06-13 14:53
 * @description
 */
public class SystemCacheUtils {

    /**
     * 秘钥缓存key
     */
    public static String getSecretKey(String appId) {
        return StrUtil.format(SystemCacheConstant.SECRET_KEY, appId).intern();
    }

    /**
     * 用户会话缓存key
     */
    public static String getUserSessionIdKey(String userId) {
        return StrUtil.format(SystemCacheConstant.USER_SESSION_ID_KEY, userId).intern();
    }
}
