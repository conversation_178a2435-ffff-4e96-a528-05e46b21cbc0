package com.linkcircle.boss.framework.web.config;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

@Component
@ConfigurationProperties(prefix = "bpm.message")
@Validated
@Data
public class BpmMessageProperties {

    private Boolean enableSms = true;

    private Boolean enableNotify = true;

    private Boolean enableMail = true;

    private Boolean enableWs = true;

}
