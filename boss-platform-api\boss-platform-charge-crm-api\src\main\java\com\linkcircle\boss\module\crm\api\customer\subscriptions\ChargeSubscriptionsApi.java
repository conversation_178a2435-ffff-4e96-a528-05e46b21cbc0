package com.linkcircle.boss.module.crm.api.customer.subscriptions;

import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.AccountSubscriptionsVO;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.Coupon;
import com.linkcircle.boss.module.crm.api.productservice.vo.ServiceCodeInfo;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.SubscribeResponseVO;
import com.linkcircle.boss.module.crm.constants.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-17 9:47
 * @description
 */
@FeignClient(name = ApiConstants.NAME,
        path = ApiConstants.PREFIX + "/subscriptions",
        fallbackFactory = ChargeSubscriptionsApiFallback.class,
        dismiss404 = true)
@Tag(name = "RPC 服务 - 客户订阅")
public interface ChargeSubscriptionsApi {

    @GetMapping("/customer/list")
    @Operation(summary = "获取客户订阅列表")
    @Parameter(name = "accountId", description = "客户账户id", required = true, example = "1024")
    CommonResult<List<AccountSubscriptionsVO>> getAccountSubscriptionsList(@RequestParam("accountId") Long accountId,
                                                                           @RequestParam(value = "paymentType", required = false) Integer paymentType);

    @GetMapping("/detail")
    @Operation(summary = "获取订阅详情")
    @Parameter(name = "subscriptionId", description = "订阅id", required = true, example = "1024")
    CommonResult<AccountSubscriptionsVO> getSubscriptionDetail(@RequestParam("subscriptionId") Long subscriptionId);

    @GetMapping("/account/ids/all")
    @Operation(summary = "根据支付类型获取所有订阅的账户ID列表")
    @Parameter(name = "paymentType", description = "支付类型 0-预付费, 1-后付费", required = true, example = "0")
    CommonResult<List<Long>> getAllSubscriptionAccountIds(@RequestParam("paymentType") Integer paymentType);

    @GetMapping("/list/by-billing-type")
    @Operation(summary = "根据计费类型查询订阅列表, 包含指定计费类型的订阅")
    @Parameter(name = "billingType", description = "计费类型 0:固定费率，1：阶梯费率，2：套餐计费，3：按量计费", required = true, example = "0")
    CommonResult<List<AccountSubscriptionsVO>> getSubscriptionsListByBillingType(@RequestParam("billingType") Integer billingType);

    @GetMapping("/charge-status")
    @Operation(summary = "更改订阅状态")
    @Parameter(name = "id", description = "订阅id", required = true, example = "1000")
    @Parameter(name = "status", description = "订阅状态 0：试用中，1：已生效，2：逾期，3：未支付，4：已取消，5：已结束 枚举：ChargeSubscriptionStatusEnum.java", required = true, example = "0")
    CommonResult<Boolean> changeSubscriptionStatus(@RequestParam("id") Long id, @RequestParam("status") Integer status);

    @GetMapping("/charge-bpm-status")
    @Operation(summary = "更改订阅审批状态")
    @Parameter(name = "id", description = "订阅id", required = true, example = "1000")
    @Parameter(name = "bpmStatus", description = "订阅审批状态", required = true, example = "0")
    CommonResult<Boolean> changeSubscriptionBpmStatus(@RequestParam("id") Long id, @RequestParam("bpmStatus") Integer bpmStatus);

    @GetMapping("/getBaseInfoBySubsId")
    @Operation(summary = "根据订阅id获取订阅基础信息")
    @Parameter(name = "id", description = "订阅id", required = true, example = "1000")
    CommonResult<SubscribeResponseVO> getBaseInfoBySubsId(@RequestParam("id") Long id);

    @GetMapping("/coupon-list")
    @Operation(summary = "获取优惠券")
    @Parameters({
            @Parameter(name = "subscriptionId", description = "订阅id", required = true, example = "1024"),
            @Parameter(name = "serviceId", description = "服务id", required = true, example = "1024")
    })
    CommonResult<List<Coupon>> getSubscriptionCouponList(@RequestParam("subscriptionId") Long subscriptionId,
                                                         @RequestParam("subscriptionId") Long serviceId);

    @GetMapping("/service-codes")
    @Operation(summary = "获取所有活跃的客户服务编码信息")
    CommonResult<List<ServiceCodeInfo>> getAllActiveServiceCodes();
}
