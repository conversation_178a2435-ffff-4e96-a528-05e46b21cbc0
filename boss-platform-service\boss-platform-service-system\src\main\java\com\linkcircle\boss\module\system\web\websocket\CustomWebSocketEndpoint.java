package com.linkcircle.boss.module.system.web.websocket;

import com.linkcircle.boss.framework.common.util.cache.SystemCacheUtils;
import com.linkcircle.boss.framework.common.util.json.JsonUtils;
import com.linkcircle.boss.module.system.api.websocket.dto.WebSocketSendReqDTO;
import io.github.kk01001.netty.annotation.OnBinaryMessage;
import io.github.kk01001.netty.annotation.OnClose;
import io.github.kk01001.netty.annotation.OnError;
import io.github.kk01001.netty.annotation.OnMessage;
import io.github.kk01001.netty.annotation.OnOpen;
import io.github.kk01001.netty.annotation.WebSocketEndpoint;
import io.github.kk01001.netty.session.WebSocketSession;
import io.github.kk01001.redis.RedissonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
@WebSocketEndpoint()
public class CustomWebSocketEndpoint {

    private final RedissonUtil redissonUtil;

    @OnOpen
    public void onOpen(WebSocketSession session) {
        String userSessionIdKey = SystemCacheUtils.getUserSessionIdKey(session.getUserId());
        Boolean result = redissonUtil.sadd(userSessionIdKey, session.getId());
        log.info("连接成功: userId={}, sessionId={}, uri={}, result={}", session.getUserId(), session.getId(), session.getUri(), result);
    }

    @OnBinaryMessage
    public void onBinaryMessage(WebSocketSession session, byte[] message) {
        log.info("收到二进制消息: userId={}, sessionId={}, message={}", session.getUserId(), session.getId(), new String(message));
    }

    @OnMessage
    public void onMessage(WebSocketSession session, String message) {
        log.info("收到消息: userId={}, sessionId={}, uri={}, message={}", session.getUserId(), session.getId(), session.getUri(), message);
        WebSocketSendReqDTO sendReqDTO = JsonUtils.parseObject(message, WebSocketSendReqDTO.class);
        // session.sendToSession(sendReqDTO.getSessionId(), sendReqDTO.getMessageContent());
    }

    @OnClose
    public void onClose(WebSocketSession session) {
        String userSessionIdKey = SystemCacheUtils.getUserSessionIdKey(session.getUserId());
        Boolean result = redissonUtil.srem(userSessionIdKey, session.getId());
        log.info("连接关闭: userId={}, sessionId={}, uri={}, result={}", session.getUserId(), session.getId(), session.getUri(), result);
    }

    @OnError
    public void onError(WebSocketSession session, Throwable error) {
        log.error("发生错误: sessionId={}", session.getId(), error);
    }
}