package com.linkcircle.boss.module.charge.fee.web.payment.service;


import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.sharding.algorithms.dto.HitBusinessTimeDTO;
import com.linkcircle.boss.module.billing.api.wallet.model.dto.WalletDeductionMqDTO;
import com.linkcircle.boss.module.fee.api.wallets.model.dto.WalletBalanceEditDTO;
import com.linkcircle.boss.module.fee.api.wallets.model.dto.WalletBalanceToPayDTO;

import java.math.BigDecimal;

public interface IWalletPaymentService {

    CommonResult<Long> walletBalanceToPay(WalletBalanceToPayDTO walletBalanceToPayDTO) throws Exception;


    /**
     * 首次支付
     *
     * @param walletDeductionMqDTO
     * @return
     * @throws Exception
     */
    boolean dealPaymentBusiness(WalletDeductionMqDTO walletDeductionMqDTO) throws Exception;


    /**
     * 充值和支付
     *
     * @param walletBalanceEditDTO
     * @return
     */
    boolean walletBalanceEdit(WalletBalanceEditDTO walletBalanceEditDTO);

    /**
     * 代充值
     *
     * @param walletId        充值钱包id
     * @param amount          充值量
     * @param walletType      充值类型 0-现金，1-积分
     * @param customerId      充值客户id
     * @param fromCustomerId  代充客户id
     * @param transactionTime 交易时间
     * @return
     */
    boolean walletBalanceProxyPay(Long walletId, BigDecimal amount, Integer walletType,
                                  Long customerId, Long fromCustomerId, Long transactionTime);

    /**
     * 划转出
     *
     * @param walletId        划转出的钱包id
     * @param amount          划转量
     * @param walletType      划转类型 0-现金，1-积分
     * @param customerId      客户id
     * @param toWalletsId     目标钱包id
     * @param transactionTime 交易时间
     * @return
     */
    boolean transferTo(Long walletId, BigDecimal amount, Integer walletType,
                       Long customerId, Long toWalletsId, Long transactionTime);

    /**
     * 划转入
     *
     * @param walletId        划算入的钱包id
     * @param amount          划转量
     * @param walletType      划转类型 0-现金，1-积分
     * @param customerId      客户id
     * @param fromWalletsId   来源钱包id
     * @param transactionTime 交易时间
     * @return
     */
    boolean transferFrom(Long walletId, BigDecimal amount, Integer walletType,
                         Long customerId, Long fromWalletsId, Long transactionTime);

    /**
     * 划转现金或者积分
     *
     * @param fromWalletsId   来源钱包id
     * @param toWalletsId     目标钱包id
     * @param amount          划转量
     * @param walletType      划转类型 0-现金，1-积分
     * @param customerId      客户id
     * @param transactionTime 交易时间
     * @return
     */
    boolean transferChargeOperation(Long fromWalletsId, Long toWalletsId, BigDecimal amount, Integer walletType, Long customerId, Long transactionTime);

    /**
     * 推送消息到重放队列
     *
     * @param walletDeductionMqDTO
     */
    void sendReplayOrder(WalletDeductionMqDTO walletDeductionMqDTO);

    void writeToLocalFile(String message);

    boolean updateNotifyResult(Long billId, Integer billType, Integer callbackStatus, HitBusinessTimeDTO businessTimeDTO) throws Exception;

}
