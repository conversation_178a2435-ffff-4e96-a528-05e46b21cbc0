package com.linkcircle.boss.module.report.web.financial.service;

import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.module.report.web.financial.model.dto.ChargeDetailTemplatePageQueryVo;
import com.linkcircle.boss.module.report.web.financial.model.dto.ChargeDetailTemplateReqDTO;
import com.linkcircle.boss.module.report.web.financial.model.vo.ChargeDetailTemplateRespVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/12 16:00
 */
public interface ChargeDetailTemplateService {
    void createTemplate(ChargeDetailTemplateReqDTO templateReqDTO);

    void updateTemplate(ChargeDetailTemplateReqDTO templateReqDTO);

    void deleteTemplate(String templateId);

    List<ChargeDetailTemplateRespVO> listTemplate(String templateName, String templateId);


    PageResult<ChargeDetailTemplateRespVO> pageQuery(ChargeDetailTemplatePageQueryVo pageQueryVo);

    ChargeDetailTemplateRespVO detail(String templateId);
}
