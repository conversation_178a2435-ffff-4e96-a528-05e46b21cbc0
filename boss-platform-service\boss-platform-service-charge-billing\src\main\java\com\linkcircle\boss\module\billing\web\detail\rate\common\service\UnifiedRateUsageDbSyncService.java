package com.linkcircle.boss.module.billing.web.detail.rate.common.service;

import com.linkcircle.boss.module.crm.enums.BillTypeEnum;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-08-05 16:21
 * @description 统一费率用量数据库同步服务接口
 */
public interface UnifiedRateUsageDbSyncService {

    /**
     * 处理数据库同步（从缓存key解析信息并同步到数据库）
     *
     * @param cacheKey 缓存key
     * @return 是否处理成功
     */
    boolean processDbSyncFromCacheKey(String cacheKey);

    /**
     * 直接处理数据库同步
     *
     * @param billType       账单类型
     * @param rateType       费率类型
     * @param serviceId      服务ID
     * @param entityId       实体ID (收入用subscriptionId，成本用purchaseId)
     * @param billingCycle   计费周期
     * @param totalUsage     总用量
     * @param billingTime    计费时间
     * @param usageUnit      用量单位
     * @param currency       货币类型
     * @param accountId      账户ID
     * @param cycleStartTime 周期开始时间
     * @param cycleEndTime   周期结束时间
     * @return 是否处理成功
     */
    boolean processDbSync(BillTypeEnum billType, String rateType, Long serviceId, Long entityId,
                         String billingCycle, BigDecimal totalUsage, Long billingTime,
                         String usageUnit, String currency, Long accountId,
                         Long cycleStartTime, Long cycleEndTime);
}
