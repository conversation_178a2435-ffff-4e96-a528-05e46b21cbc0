# 缓存常量重构说明

## 📋 重构概述

将费率用量缓存相关的常量从业务模块中提取到框架层统一管理，提高代码的可维护性和一致性。

## 🎯 重构目标

1. **统一管理**：将所有缓存相关常量集中在框架层管理
2. **避免重复**：消除硬编码的缓存key前缀
3. **提高一致性**：确保所有模块使用相同的缓存key格式
4. **便于维护**：缓存key格式变更时只需修改一处

## 🔧 重构内容

### 1. 新增常量定义

#### **ChargeCacheConstant.java**
```java
/**
 * hash
 * 统一费率用量缓存前缀
 * billing:usage:{bill_type}:{rate_type}:{service_id}:{entity_id}:{billing_cycle}
 * bill_type: income/cost
 * rate_type: package/usage
 * entity_id: subscription_id(收入) 或 purchase_id(成本)
 */
String UNIFIED_RATE_USAGE_CACHE_PREFIX = PREFIX + "billing:usage:{}:{}:{}:{}:{}";

/**
 * zset
 * 费率用量同步分片前缀
 * sync:zset:shard:{shard_index}
 * 用于ZSet分片存储待同步的缓存key，按时间戳排序
 */
String RATE_USAGE_SYNC_SHARD_PREFIX = PREFIX + "sync:zset:shard:{}";
```

### 2. 新增工具方法

#### **ChargeCacheUtils.java**
```java
/**
 * 获取统一费率用量缓存key
 */
public static String getUnifiedRateUsageCacheKey(String billType, String rateType, 
                                               Long serviceId, Long entityId, String billingCycle) {
    return StrFormatter.format(ChargeCacheConstant.UNIFIED_RATE_USAGE_CACHE_PREFIX, 
                              billType, rateType, serviceId, entityId, billingCycle);
}

/**
 * 获取费率用量同步分片key
 */
public static String getRateUsageSyncShardKey(int shardIndex) {
    return StrFormatter.format(ChargeCacheConstant.RATE_USAGE_SYNC_SHARD_PREFIX, shardIndex);
}
```

## 📁 修改的文件

### 1. 框架层文件

#### **boss-platform-framework/boss-platform-common/src/main/java/com/linkcircle/boss/framework/common/constants/ChargeCacheConstant.java**
- ✅ 新增 `UNIFIED_RATE_USAGE_CACHE_PREFIX` 常量
- ✅ 新增 `RATE_USAGE_SYNC_SHARD_PREFIX` 常量

#### **boss-platform-framework/boss-platform-common/src/main/java/com/linkcircle/boss/framework/common/util/cache/ChargeCacheUtils.java**
- ✅ 新增 `getUnifiedRateUsageCacheKey()` 方法
- ✅ 新增 `getRateUsageSyncShardKey()` 方法

### 2. 业务层文件

#### **UnifiedRateUsageCacheServiceImpl.java**
```java
// 重构前
private String buildCacheKey(BillTypeEnum billType, String rateType, Long serviceId, Long entityId, String billingCycle) {
    return String.format("boss:charge:billing:usage:%s:%s:%s:%s:%s", 
            billType.name().toLowerCase(), rateType, serviceId, entityId, billingCycle);
}

// 重构后
private String buildCacheKey(BillTypeEnum billType, String rateType, Long serviceId, Long entityId, String billingCycle) {
    return ChargeCacheUtils.getUnifiedRateUsageCacheKey(
            billType.name().toLowerCase(), rateType, serviceId, entityId, billingCycle);
}
```

#### **RateUsageSyncTracker.java**
```java
// 重构前
private static final String SYNC_ZSET_PREFIX = "boss:charge:sync:zset:shard:";
String shardKey = SYNC_ZSET_PREFIX + shardIndex;

// 重构后
String shardKey = ChargeCacheUtils.getRateUsageSyncShardKey(shardIndex);
```

## 🔍 缓存Key格式说明

### 1. 统一费率用量缓存Key
```
格式：boss:charge:billing:usage:{bill_type}:{rate_type}:{service_id}:{entity_id}:{billing_cycle}

示例：
- 收入套餐费率：boss:charge:billing:usage:income:package:1001:2001:20250801-20250831
- 收入按量费率：boss:charge:billing:usage:income:usage:1001:2001:20250801-20250831
- 成本套餐费率：boss:charge:billing:usage:cost:package:1001:3001:20250801-20250831
- 成本按量费率：boss:charge:billing:usage:cost:usage:1001:3001:20250801-20250831

参数说明：
- bill_type: income(收入) 或 cost(成本)
- rate_type: package(套餐) 或 usage(按量)
- service_id: 服务ID
- entity_id: 实体ID（收入用subscription_id，成本用purchase_id）
- billing_cycle: 计费周期（格式：yyyyMMdd-yyyyMMdd）
```

### 2. 费率用量同步分片Key
```
格式：boss:charge:sync:zset:shard:{shard_index}

示例：
- 分片0：boss:charge:sync:zset:shard:0
- 分片1：boss:charge:sync:zset:shard:1
- 分片99：boss:charge:sync:zset:shard:99

参数说明：
- shard_index: 分片索引（0到分片数量-1）
```

## ✅ 重构优势

### 1. **统一管理**
- 所有缓存key格式在框架层统一定义
- 避免业务模块中的硬编码
- 便于统一修改和维护

### 2. **类型安全**
- 使用工具方法构建缓存key，避免拼接错误
- 参数类型明确，减少运行时错误
- IDE可以提供更好的代码提示

### 3. **易于扩展**
- 新增缓存类型时只需在框架层添加常量和方法
- 业务模块无需关心具体的key格式
- 支持缓存key格式的统一升级

### 4. **便于调试**
- 缓存key格式集中管理，便于问题排查
- 可以在工具方法中添加日志和验证逻辑
- 支持缓存key的统一监控和分析

## 🔄 使用示例

### 1. 构建缓存Key
```java
// 收入套餐费率用量缓存key
String cacheKey = ChargeCacheUtils.getUnifiedRateUsageCacheKey(
    "income", "package", 1001L, 2001L, "20250801-20250831");

// 成本按量费率用量缓存key
String cacheKey = ChargeCacheUtils.getUnifiedRateUsageCacheKey(
    "cost", "usage", 1001L, 3001L, "20250801-20250831");
```

### 2. 构建分片Key
```java
// 获取分片0的key
String shardKey = ChargeCacheUtils.getRateUsageSyncShardKey(0);

// 获取分片99的key
String shardKey = ChargeCacheUtils.getRateUsageSyncShardKey(99);
```

## 📈 后续优化建议

1. **缓存Key验证**：在工具方法中添加参数验证逻辑
2. **缓存Key监控**：添加缓存key使用情况的监控
3. **缓存Key文档**：自动生成缓存key格式文档
4. **缓存Key迁移**：支持缓存key格式的平滑迁移

## ⚠️ 注意事项

1. **向后兼容**：确保重构后的缓存key格式与现有数据兼容
2. **测试验证**：充分测试缓存key的正确性
3. **文档更新**：及时更新相关文档和注释
4. **团队沟通**：确保团队成员了解新的缓存key管理方式

---

**重构完成时间**：2025-08-05  
**负责人**：linshiqiang  
**版本**：v1.0
