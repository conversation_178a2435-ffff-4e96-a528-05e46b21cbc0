package com.linkcircle.boss.module.billing.web.detail.rate.income.mapper;

import com.linkcircle.boss.framework.mybatis.core.mapper.BaseMapperX;
import com.linkcircle.boss.module.billing.web.detail.rate.income.model.entity.IncomeFixedRateCycleStatusDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-06-23 09:02
 * @description 收入-固定费率周期状态表数据访问层
 */
@Mapper
public interface IncomeFixedRateCycleStatusMapper extends BaseMapperX<IncomeFixedRateCycleStatusDO> {

    /**
     * 累加总用量
     */
    int updateTotalUsageByCondition(@Param("serviceId") Long serviceId,
                                    @Param("subscriptionId") Long subscriptionId,
                                    @Param("billingCycle") String billingCycle,
                                    @Param("usage") BigDecimal usage,
                                    @Param("nowTime") Long nowTime);

}
