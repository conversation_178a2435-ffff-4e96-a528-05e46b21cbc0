package com.linkcircle.boss.framework.common.constants;

/**
 * <AUTHOR>
 * @date 2025-06-13 14:44
 * @description 计费缓存常量
 */
public interface ChargeCacheConstant {

    String PREFIX = "boss:charge:";

    /**
     * string
     * 请求ID唯一性缓存前缀
     * request_id:{request_id}
     */
    String REQUEST_ID_PREFIX = PREFIX + "request_id:{}";

    /**
     * string
     * 业务ID唯一性缓存前缀
     * business_id:{business_id}
     */
    String BUSINESS_ID_PREFIX = PREFIX + "business_id:{}";

    /**
     * string
     * 账户订阅信息缓存前缀
     * subscriptions:{account_id}
     */
    String ACCOUNT_SUBSCRIPTIONS_PREFIX = PREFIX + "subscriptions:{}";

    /**
     * string
     * 订阅信息详情缓存前缀
     * subscription:{subscription_id}
     */
    String SUBSCRIPTION_DETAIL_PREFIX = PREFIX + "subscription:{}";

    /**
     * string
     * 供应商资源采购信息缓存前缀
     * resource_purchases:{account_id}
     */
    String ACCOUNT_RESOURCE_PURCHASES_PREFIX = PREFIX + "resource_purchases:{}";

    /**
     * string
     * 供应商资源采购信息详情缓存前缀
     * resource_purchases:{purchase_id}
     */
    String RESOURCE_PURCHASE_DETAIL_PREFIX = PREFIX + "purchase:{}";

    /**
     * string
     * 账单ID序号缓存前缀
     * bill_id_seq:{type}:{}
     */
    String BILL_ID_SEQUENCE_PREFIX = PREFIX + "bill_id_seq:{}:{}";

    /**
     * string
     * 客户账户信息缓存前缀
     * customer_account:{account_id}
     */
    String CUSTOMER_ACCOUNT_PREFIX = PREFIX + "customer_account:{}";

    /**
     * string
     * 供应商账户信息缓存前缀
     * supplier_account:{account_id}
     */
    String SUPPLIER_ACCOUNT_PREFIX = PREFIX + "supplier_account:{}";

    /**
     * 订阅消耗量 费率计费缓存前缀
     * billing:rate_usage:{bill_type}:{rate_type}:{dimension_key}:{billing_cycle}
     * bill_type: income/cost
     * rate_type: fixed/tiered/package/usage
     * dimension_key: subscription_id 或 account_id:service_id
     */
    String RATE_BILLING_PREFIX = PREFIX + "billing:rate_usage:{}:{}:{}:{}";

    /**
     * string
     * 量表字段配置信息缓存前缀
     * scale_columns:{scale_id}
     */
    String SCALE_COLUMNS_PREFIX = PREFIX + "scale_columns:{}";

    /**
     * string
     * 指标单位配置简单列表缓存key
     * metric_unit_config_simple_list
     */
    String METRIC_UNIT_CONFIG_SIMPLE_LIST = PREFIX + "metric_unit_config_simple_list";

    /**
     * string
     * 账户处理分布式锁前缀
     * lock:account:{account_id}
     */
    String ACCOUNT_PROCESS_LOCK_PREFIX = PREFIX + "lock:account:{}";

    /**
     * string
     * 处理分布式锁前缀
     * process_lock:{type}:{id}
     */
    String PROCESS_LOCK_PREFIX = PREFIX + "process_lock:{}:{}";

    /**
     * 支付账单账户锁前缀
     */
    String CONSUMER_PAYMENT_ORDER_LOCK_PREFIX = PREFIX + "payment:lock:account:{}";

    /**
     * 下载任务-增量前缀
     */
    String CONSUMER_DOWNLOAD_TASK_INCREMENT_PREFIX = PREFIX + "download:task:increment:{}";

    /**
     * 服务账单是否出账完成
     * exist:service:bill:{service_code}:{subscription_id}:{cycle_start}:{cycle_end}
     */
    String SERVICE_BILL_CACHE_PREFIX = PREFIX + "exist:service:bill:{}:{}:{}:{}";
    
    /**
     * 产品账单是否出账完成
     * exist:product:bill:{product_id}:{subscription_id}:{cycle_start}:{cycle_end}
     */
    String PRODUCT_BILL_CACHE_PREFIX = PREFIX + "exist:product:bill:{}:{}:{}:{}";
    
    /**
     * 资源服务账单是否出账完成
     * exist:resource:service:bill:{service_code}:{purchase_id}:{cycle_start}:{cycle_end}
     */
    String RESOURCE_SERVICE_BILL_CACHE_PREFIX = PREFIX + "exist:resource:service:bill:{}:{}:{}:{}";

    /**
     * 计费防重复处理锁前缀
     * billing:prevent:duplicate:{rate_type}:{subscription_id}:{service_id}:{billing_cycle}
     */
    String BILLING_PREVENT_DUPLICATE_PREFIX = PREFIX + "billing:prevent:duplicate:{}:{}:{}:{}";

    /**
     * hash
     * 费率用量缓存前缀
     * billing:usage:{rate_type}:{service_id}:{subscription_id}:{billing_cycle}
     * rate_type: package/usage
     */
    String RATE_USAGE_CACHE_PREFIX = PREFIX + "billing:usage:{}:{}:{}:{}";

    /**
     * hash
     * 统一费率用量缓存前缀
     * billing:usage:{bill_type}:{rate_type}:{service_id}:{entity_id}:{billing_cycle}
     * bill_type: income/cost
     * rate_type: package/usage
     * entity_id: subscription_id(收入) 或 purchase_id(成本)
     */
    String UNIFIED_RATE_USAGE_CACHE_PREFIX = PREFIX + "billing:usage:{}:{}:{}:{}:{}";

    /**
     * zset
     * 费率用量同步分片前缀
     * sync:zset:shard:{shard_index}
     * 用于ZSet分片存储待同步的缓存key，按时间戳排序
     */
    String RATE_USAGE_SYNC_SHARD_PREFIX = PREFIX + "sync:zset:shard:{}";
}
