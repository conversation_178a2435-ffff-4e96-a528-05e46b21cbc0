package com.linkcircle.boss.module.billing.web.detail.rate.income.service.impl;

import io.github.kk01001.redis.RedissonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025-08-05 16:21
 * @description 分片式费率用量同步跟踪器 - 适用于超大规模场景
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RateUsageShardedSyncTracker {

    private final RedissonUtil redissonUtil;

    // 分片数量，可以根据实际情况调整
    private static final int SHARD_COUNT = 16;
    private static final String SYNC_SHARD_PREFIX = "boss:charge:sync:shard:";
    private static final DateTimeFormatter HOUR_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHH");

    /**
     * 添加待同步的key（分片存储）
     *
     * @param cacheKey 缓存key
     */
    public void addPendingSync(String cacheKey) {
        try {
            // 根据key的hash值确定分片
            int shardIndex = Math.abs(cacheKey.hashCode()) % SHARD_COUNT;
            
            // 按小时分片，避免单个Set过大
            String hour = LocalDateTime.now().format(HOUR_FORMATTER);
            String shardKey = SYNC_SHARD_PREFIX + hour + ":" + shardIndex;
            
            redissonUtil.sadd(shardKey, cacheKey);
            // 设置2小时过期时间
            redissonUtil.expire(shardKey, Duration.ofHours(2));
            
            log.debug("添加待同步key到分片: {}, key: {}", shardKey, cacheKey);
        } catch (Exception e) {
            log.error("添加待同步key到分片失败: {}", cacheKey, e);
        }
    }

    /**
     * 获取指定分片的待同步key
     *
     * @param hour       小时标识
     * @param shardIndex 分片索引
     * @param batchSize  批次大小
     * @return 待同步的key集合
     */
    public Set<String> getPendingKeysFromShard(String hour, int shardIndex, int batchSize) {
        try {
            String shardKey = SYNC_SHARD_PREFIX + hour + ":" + shardIndex;
            Set<String> keys = redissonUtil.spop(shardKey, batchSize);
            
            if (keys != null && !keys.isEmpty()) {
                log.debug("从分片{}获取待同步key数量: {}", shardKey, keys.size());
            }
            
            return keys;
        } catch (Exception e) {
            log.error("从分片获取待同步key失败, hour: {}, shardIndex: {}", hour, shardIndex, e);
            return Set.of();
        }
    }

    /**
     * 获取所有分片的待同步key总数
     *
     * @param hour 小时标识
     * @return 总数
     */
    public long getTotalPendingCount(String hour) {
        long total = 0;
        try {
            for (int i = 0; i < SHARD_COUNT; i++) {
                String shardKey = SYNC_SHARD_PREFIX + hour + ":" + i;
                total += redissonUtil.scard(shardKey);
            }
        } catch (Exception e) {
            log.error("获取分片待同步key总数失败, hour: {}", hour, e);
        }
        return total;
    }

    /**
     * 清理过期的分片
     *
     * @param hoursToKeep 保留的小时数
     */
    public void cleanupExpiredShards(int hoursToKeep) {
        try {
            LocalDateTime cutoffTime = LocalDateTime.now().minusHours(hoursToKeep);
            
            // 这里可以实现清理逻辑，删除过期的分片
            // 由于Redis已经设置了过期时间，这里主要是记录日志
            log.info("清理{}小时前的过期分片", hoursToKeep);
            
        } catch (Exception e) {
            log.error("清理过期分片失败", e);
        }
    }

    /**
     * 获取当前小时标识
     *
     * @return 当前小时标识
     */
    public String getCurrentHour() {
        return LocalDateTime.now().format(HOUR_FORMATTER);
    }

    /**
     * 获取分片数量
     *
     * @return 分片数量
     */
    public int getShardCount() {
        return SHARD_COUNT;
    }
}
