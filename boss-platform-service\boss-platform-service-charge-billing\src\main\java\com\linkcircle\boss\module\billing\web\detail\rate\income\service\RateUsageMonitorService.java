package com.linkcircle.boss.module.billing.web.detail.rate.income.service;

/**
 * <AUTHOR>
 * @date 2025-08-05 16:21
 * @description 费率用量监控服务接口
 */
public interface RateUsageMonitorService {

    /**
     * 检查缓存和数据库数据一致性
     */
    void checkDataConsistency();

    /**
     * 获取缓存命中率
     *
     * @return 缓存命中率百分比
     */
    double getCacheHitRate();

    /**
     * 获取MQ发送成功率
     *
     * @return MQ发送成功率百分比
     */
    double getMqSendSuccessRate();

    /**
     * 获取数据同步延迟统计
     *
     * @return 平均延迟毫秒数
     */
    long getDataSyncDelayMs();

    /**
     * 重置监控指标
     */
    void resetMetrics();
}
