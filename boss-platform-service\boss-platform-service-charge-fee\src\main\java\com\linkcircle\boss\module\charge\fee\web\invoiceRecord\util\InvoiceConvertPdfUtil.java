package com.linkcircle.boss.module.charge.fee.web.invoiceRecord.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import com.linkcircle.boss.framework.common.util.pdf.PdfRenderer;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.BillContent;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.BillCoupon;
import com.linkcircle.boss.module.charge.fee.web.invoiceRecord.model.vo.InvoiceDetailsResVO;
import com.linkcircle.boss.module.crm.api.basicConfig.vo.InvoiceDetailsVO;
import com.linkcircle.boss.module.crm.api.customer.customer.vo.ChargeCustomerInfoVO;
import com.linkcircle.boss.module.crm.api.entity.vo.EntityDetailsVO;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 发票转换 pdf工具类
 *
 * <AUTHOR> zyuan
 * @data : 2025-07-01
 */
@Slf4j
public class InvoiceConvertPdfUtil {

    /**
     * 生成pdf
     *
     * @param invoiceInfo      发票属性
     * @param cssPath          样式模版
     * @param templateFileName pdf生成模版名称
     * @return 文件字节
     */
    public static byte[] createPdf(InvoiceDetailsResVO invoiceInfo,
                                   String cssPath,
                                   String templateFileName) {
        try {
            Map<String, Object> data = new HashMap<>();
            EntityDetailsVO entityDetailsVO = invoiceInfo.getEntityInfo();
            Map<String, Object> entityInfo = new HashMap<>();
            entityInfo.put("brandLogoUrl", entityDetailsVO.getBrandLogoUrl());
            entityInfo.put("entityName", entityDetailsVO.getEntityName());
            entityInfo.put("address", entityDetailsVO.getAddress());
            entityInfo.put("postalCode", entityDetailsVO.getPostalCode());
            entityInfo.put("taxNumber", entityDetailsVO.getTaxNumber());
            entityInfo.put("city", entityDetailsVO.getCity());
            entityInfo.put("email", entityDetailsVO.getEmail());

            ChargeCustomerInfoVO customerInfoVO = invoiceInfo.getCustomerInfo();
            Map<String, Object> customerInfo = new HashMap<>();
            customerInfo.put("customerName", customerInfoVO.getCustomerName());
            customerInfo.put("address", customerInfoVO.getAddress());

            InvoiceDetailsVO config = invoiceInfo.getInvoiceConfig();
            Map<String, Object> invoiceConfig = new HashMap<>();
            invoiceConfig.put("startSequence", invoiceInfo.getInvoiceId());
            invoiceConfig.put("legalInformation", config.getLegalInformation().replace("\n", "<br>"));
            invoiceConfig.put("additionalInformation", config.getAdditionalInformation().replace("\n", "<br>"));
            invoiceConfig.put("color", config.getBrandColor());
            Map<String, Object> entity = new HashMap<>();
            entity.put("date", invoiceInfo.getInvoiceDate());
            entity.put("entityInfo", entityInfo);
            entity.put("customerInfo", customerInfo);
            entity.put("invoiceConfig", invoiceConfig);

            // 账单基础信息
            List<Map<String, Object>> inputArray = convertToMapList(invoiceInfo.getShowContents());
            List<Map<String, Object>> showContents = JsonArrayToMapConverter.convertArray(inputArray);
            List<Map<String, Object>> maps = Test.generateSampleData();
            entity.put("showContents", maps);

            // 优惠信息
            List<BillCoupon> showCoupons = new ArrayList<>();
            BillCoupon b1 = new BillCoupon();
            b1.setDescription("AI智能通信折扣(10%)");
            b1.setDiscountPrice("-S$ 2.50");
            b1.setTotalAmount("-S$ 2.50");
            BillCoupon b2 = new BillCoupon();
            b2.setDescription("二次优惠折扣(- S$ 2.00)");
            b2.setDiscountPrice("-S$ 2.50");
            b2.setTotalAmount("-S$ 2.50");
            showCoupons.add(b1);
            showCoupons.add(b2);

            // 总计信息
            entity.put("amountWithoutTax", invoiceInfo.getAmountWithoutTax());
            entity.put("discountAmount", invoiceInfo.getDiscountAmount());
            entity.put("taxAmount", invoiceInfo.getTaxAmount());
            entity.put("subTotalAmount", invoiceInfo.getSubTotalAmount());
            entity.put("amountWithTax", invoiceInfo.getAmountWithTax());
            entity.put("showCoupons", showCoupons);
            entity.put("startingDateOfBill", invoiceInfo.getStartingDateOfBill());
            data.put("entity", entity);
            data.put("title", "变形金刚发票公司");
            return new PdfRenderer.Builder()
                    .withTemplate(templateFileName)
                    .withCssFileName(cssPath)
                    .withData(data)
                    .render();
        } catch (Exception e) {
            log.error("转换发票pdf文件异常：", e);
            return null;
        }
    }

    public static List<Map<String, Object>> convertToMapList(List<BillContent> billList) {
        return billList.stream()
                .map(bill -> {
                    Map<String, Object> map = MapUtil.newHashMap();
                    BeanUtil.copyProperties(bill, map);

                    // 处理children递归
                    if (bill.getChildren() != null) {
                        map.put("children", convertToMapList(bill.getChildren()));
                    }
                    return map;
                })
                .collect(Collectors.toList());
    }

    public static void main(String[] args) {
        // 模拟输入数据（数组形式）
        List<Map<String, Object>> inputArray = new ArrayList<>();

        // 第一个元素
        Map<String, Object> item1 = new LinkedHashMap<>();
        item1.put("description", "按量计费");
        item1.put("discountUnitPrice", "");
        item1.put("usageCount", "");
        item1.put("discountPrice", "");
        item1.put("taxRate", "");
        item1.put("totalAmount", "");
        item1.put("currency", "");

        List<Map<String, Object>> children1 = new ArrayList<>();
        Map<String, Object> child1 = new LinkedHashMap<>();
        child1.put("description", "按量计费服务(15个/5周)");
        child1.put("discountUnitPrice", "180");
        child1.put("usageCount", "15");
        child1.put("discountPrice", "2700");
        child1.put("taxRate", "9.000000%");
        child1.put("totalAmount", "2943.000000");
        child1.put("currency", "CNY");
        child1.put("children", new ArrayList<>());
        children1.add(child1);

        item1.put("children", children1);
        inputArray.add(item1);

        // 可以添加更多数组元素...

        // 转换
        List<Map<String, Object>> result = JsonArrayToMapConverter.convertArray(inputArray);

        // 打印结果
        System.out.println("转换结果:");
        result.forEach(System.out::println);
    }
}
