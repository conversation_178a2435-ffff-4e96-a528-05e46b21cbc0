package com.linkcircle.boss.module.bpm.convert.message;

import com.linkcircle.boss.module.system.api.mail.dto.MailSendSingleToUserReqDTO;
import com.linkcircle.boss.module.system.api.notify.dto.NotifySendSingleToUserReqDTO;
import com.linkcircle.boss.module.system.api.sms.dto.send.SmsSendSingleToUserReqDTO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.Map;

@Mapper
public interface BpmMessageConvert {

    BpmMessageConvert INSTANCE = Mappers.getMapper(BpmMessageConvert.class);

    @Mapping(target = "mobile", ignore = true)
    @Mapping(source = "userId", target = "userId")
    @Mapping(source = "templateCode", target = "templateCode")
    @Mapping(source = "templateParams", target = "templateParams")
    SmsSendSingleToUserReqDTO convert(Long userId, String templateCode, Map<String, Object> templateParams);

    @Mapping(source = "userId", target = "userId")
    @Mapping(source = "templateCode", target = "templateCode")
    @Mapping(source = "templateParams", target = "templateParams")
    NotifySendSingleToUserReqDTO convert2NotifyDTO(Long userId, String templateCode, Map<String, Object> templateParams);

    @Mapping(target = "mail", ignore = true)
    @Mapping(source = "userId", target = "userId")
    @Mapping(source = "templateCode", target = "templateCode")
    @Mapping(source = "templateParams", target = "templateParams")
    @Valid MailSendSingleToUserReqDTO convert2MailDTO(Long userId, String templateCode, Map<String, Object> templateParams);

}
