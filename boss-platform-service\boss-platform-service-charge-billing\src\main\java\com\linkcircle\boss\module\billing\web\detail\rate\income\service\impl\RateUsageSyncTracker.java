package com.linkcircle.boss.module.billing.web.detail.rate.income.service.impl;

import io.github.kk01001.redis.RedissonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025-08-05 16:21
 * @description 费率用量同步跟踪器 - 使用Redis Set记录待同步的key
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RateUsageSyncTracker {

    private final RedissonUtil redissonUtil;

    // Redis Set key，用于记录待同步的缓存key
    private static final String SYNC_PENDING_SET = "boss:charge:sync:pending:set";
    private static final String SYNC_PROCESSING_SET = "boss:charge:sync:processing:set";

    /**
     * 添加待同步的key
     *
     * @param cacheKey 缓存key
     */
    public void addPendingSync(String cacheKey) {
        try {
            redissonUtil.sadd(SYNC_PENDING_SET, cacheKey);
            // 设置Set的过期时间为1天，防止无限增长
            redissonUtil.expire(SYNC_PENDING_SET, Duration.ofDays(1));
            log.debug("添加待同步key: {}", cacheKey);
        } catch (Exception e) {
            log.error("添加待同步key失败: {}", cacheKey, e);
        }
    }

    /**
     * 获取并移除一批待同步的key
     *
     * @param batchSize 批次大小
     * @return 待同步的key集合
     */
    public Set<String> getAndRemovePendingKeys(int batchSize) {
        try {
            // 使用SPOP批量弹出元素，原子操作
            Set<String> keys = redissonUtil.spop(SYNC_PENDING_SET, batchSize);
            if (keys != null && !keys.isEmpty()) {
                // 将这些key添加到处理中的Set，用于异常恢复
                for (String key : keys) {
                    redissonUtil.sadd(SYNC_PROCESSING_SET, key);
                }
                redissonUtil.expire(SYNC_PROCESSING_SET, Duration.ofHours(1));
                log.debug("获取待同步key数量: {}", keys.size());
            }
            return keys;
        } catch (Exception e) {
            log.error("获取待同步key失败", e);
            return Set.of();
        }
    }

    /**
     * 标记key同步完成
     *
     * @param cacheKey 缓存key
     */
    public void markSyncCompleted(String cacheKey) {
        try {
            redissonUtil.srem(SYNC_PROCESSING_SET, cacheKey);
            log.debug("标记同步完成: {}", cacheKey);
        } catch (Exception e) {
            log.error("标记同步完成失败: {}", cacheKey, e);
        }
    }

    /**
     * 恢复处理中的key到待同步Set（用于异常恢复）
     */
    public void recoverProcessingKeys() {
        try {
            Set<String> processingKeys = redissonUtil.smembers(SYNC_PROCESSING_SET);
            if (processingKeys != null && !processingKeys.isEmpty()) {
                for (String key : processingKeys) {
                    redissonUtil.sadd(SYNC_PENDING_SET, key);
                }
                redissonUtil.del(SYNC_PROCESSING_SET);
                log.info("恢复处理中的key到待同步Set，数量: {}", processingKeys.size());
            }
        } catch (Exception e) {
            log.error("恢复处理中的key失败", e);
        }
    }

    /**
     * 获取待同步key数量
     *
     * @return 待同步key数量
     */
    public long getPendingCount() {
        try {
            return redissonUtil.scard(SYNC_PENDING_SET);
        } catch (Exception e) {
            log.error("获取待同步key数量失败", e);
            return 0;
        }
    }

    /**
     * 获取处理中key数量
     *
     * @return 处理中key数量
     */
    public long getProcessingCount() {
        try {
            return redissonUtil.scard(SYNC_PROCESSING_SET);
        } catch (Exception e) {
            log.error("获取处理中key数量失败", e);
            return 0;
        }
    }

    /**
     * 清理所有同步相关的Set
     */
    public void clearAllSyncSets() {
        try {
            redissonUtil.del(SYNC_PENDING_SET);
            redissonUtil.del(SYNC_PROCESSING_SET);
            log.info("清理所有同步相关的Set完成");
        } catch (Exception e) {
            log.error("清理同步Set失败", e);
        }
    }
}
