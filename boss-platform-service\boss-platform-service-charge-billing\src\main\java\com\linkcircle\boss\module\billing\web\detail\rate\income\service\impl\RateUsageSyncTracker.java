package com.linkcircle.boss.module.billing.web.detail.rate.income.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.linkcircle.boss.module.billing.config.ChargeBillingProperties;
import io.github.kk01001.redis.RedissonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025-08-05 16:21
 * @description 费率用量同步跟踪器 - 使用Redis ZSet分片存储，按时间戳排序
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RateUsageSyncTracker {

    private final RedissonUtil redissonUtil;
    private final ChargeBillingProperties chargeBillingProperties;

    // ZSet分片配置
    private static final String SYNC_ZSET_PREFIX = "boss:charge:sync:zset:shard:";

    /**
     * 添加或更新待同步的key（使用当前时间戳作为score）
     *
     * @param cacheKey 缓存key
     */
    public void addPendingSync(String cacheKey) {
        try {
            // 根据key的hash值确定分片
            int shardIndex = getShardIndex(cacheKey);
            String shardKey = SYNC_ZSET_PREFIX + shardIndex;

            // 使用当前时间戳作为score，确保按时间排序
            long timestamp = System.currentTimeMillis();
            redissonUtil.zadd(shardKey, timestamp, cacheKey);

            // 设置过期时间为1天
            redissonUtil.expire(shardKey, Duration.ofDays(1));

            // 检查分片大小，如果超过限制则清理旧数据
            checkAndCleanShard(shardKey);

            log.debug("添加待同步key到分片{}: {}, timestamp: {}", shardIndex, cacheKey, timestamp);
        } catch (Exception e) {
            log.error("添加待同步key失败: {}", cacheKey, e);
        }
    }

    /**
     * 从指定分片获取并移除一批待同步的key（按时间戳排序，最旧的优先）
     *
     * @param shardIndex 分片索引
     * @param batchSize  批次大小
     * @return 待同步的key集合
     */
    public Set<String> getAndRemovePendingKeys(int shardIndex, int batchSize) {
        try {
            String shardKey = SYNC_ZSET_PREFIX + shardIndex;

            // 获取最旧的batchSize个元素（score最小的）
            Set<String> keys = redissonUtil.zrange(shardKey, 0, batchSize - 1);

            if (CollUtil.isNotEmpty(keys)) {
                // 从ZSet中移除这些key
                for (String key : keys) {
                    redissonUtil.zrem(shardKey, key);
                }
                log.debug("从分片{}获取待同步key数量: {}", shardIndex, keys.size());
            }

            return keys != null ? keys : Set.of();
        } catch (Exception e) {
            log.error("从分片{}获取待同步key失败", shardIndex, e);
            return Set.of();
        }
    }

    /**
     * 获取所有分片的待同步key（轮询方式）
     *
     * @param batchSize 批次大小
     * @return 待同步的key集合
     */
    public Set<String> getAndRemovePendingKeys(int batchSize) {
        try {
            // 轮询所有分片，每个分片取少量数据
            int shardCount = getShardCount();
            int perShardSize = Math.max(1, batchSize / shardCount);
            Set<String> allKeys = CollUtil.newHashSet();

            for (int i = 0; i < shardCount && allKeys.size() < batchSize; i++) {
                Set<String> shardKeys = getAndRemovePendingKeys(i, perShardSize);
                allKeys.addAll(shardKeys);
            }

            log.debug("轮询所有分片获取待同步key数量: {}", allKeys.size());
            return allKeys;
        } catch (Exception e) {
            log.error("轮询获取待同步key失败", e);
            return Set.of();
        }
    }

    /**
     * 移除已同步的key（ZSet中已经在获取时移除，这里主要用于日志）
     *
     * @param cacheKey 缓存key
     */
    public void markSyncCompleted(String cacheKey) {
        log.debug("标记同步完成: {}", cacheKey);
    }

    /**
     * 重新添加同步失败的key（更新时间戳）
     *
     * @param cacheKey 缓存key
     */
    public void reAddFailedKey(String cacheKey) {
        try {
            // 同步失败的key重新添加，使用新的时间戳
            addPendingSync(cacheKey);
            log.debug("重新添加同步失败的key: {}", cacheKey);
        } catch (Exception e) {
            log.error("重新添加同步失败的key失败: {}", cacheKey, e);
        }
    }

    /**
     * 获取所有分片的待同步key总数
     *
     * @return 待同步key总数
     */
    public long getPendingCount() {
        try {
            long total = 0;
            int shardCount = getShardCount();
            for (int i = 0; i < shardCount; i++) {
                String shardKey = SYNC_ZSET_PREFIX + i;
                total += redissonUtil.zcard(shardKey);
            }
            return total;
        } catch (Exception e) {
            log.error("获取待同步key总数失败", e);
            return 0;
        }
    }

    /**
     * 获取指定分片的待同步key数量
     *
     * @param shardIndex 分片索引
     * @return 分片中的key数量
     */
    public long getShardPendingCount(int shardIndex) {
        try {
            String shardKey = SYNC_ZSET_PREFIX + shardIndex;
            return redissonUtil.zcard(shardKey);
        } catch (Exception e) {
            log.error("获取分片{}待同步key数量失败", shardIndex, e);
            return 0;
        }
    }

    /**
     * 清理所有分片的ZSet
     */
    public void clearAllShards() {
        try {
            int shardCount = getShardCount();
            for (int i = 0; i < shardCount; i++) {
                String shardKey = SYNC_ZSET_PREFIX + i;
                redissonUtil.del(shardKey);
            }
            log.info("清理所有分片ZSet完成，分片数量: {}", shardCount);
        } catch (Exception e) {
            log.error("清理分片ZSet失败", e);
        }
    }

    /**
     * 清理过期的key（删除指定时间之前的数据）
     *
     * @param beforeTimestamp 时间戳阈值
     * @return 清理的key总数
     */
    public long cleanupExpiredKeys(long beforeTimestamp) {
        try {
            long totalCleaned = 0;
            int shardCount = getShardCount();
            for (int i = 0; i < shardCount; i++) {
                String shardKey = SYNC_ZSET_PREFIX + i;
                // 删除score小于指定时间戳的元素
                long cleaned = redissonUtil.zremrangebyscore(shardKey, 0, beforeTimestamp);
                totalCleaned += cleaned;
            }
            log.info("清理过期key完成，清理数量: {}, 时间戳阈值: {}", totalCleaned, beforeTimestamp);
            return totalCleaned;
        } catch (Exception e) {
            log.error("清理过期key失败, 时间戳阈值: {}", beforeTimestamp, e);
            return 0;
        }
    }

    /**
     * 根据key计算分片索引
     *
     * @param cacheKey 缓存key
     * @return 分片索引
     */
    private int getShardIndex(String cacheKey) {
        return Math.abs(cacheKey.hashCode()) % getShardCount();
    }

    /**
     * 检查并清理分片大小（保持每个分片不超过限制）
     *
     * @param shardKey 分片key
     */
    private void checkAndCleanShard(String shardKey) {
        try {
            long size = redissonUtil.zcard(shardKey);
            int maxShardSize = getMaxShardSize();
            if (size > maxShardSize) {
                // 删除最旧的元素，保留最新的maxShardSize个
                long toRemove = size - maxShardSize;
                redissonUtil.zremrangebyrank(shardKey, 0, toRemove - 1);
                log.info("分片{}超过大小限制，清理了{}个旧元素", shardKey, toRemove);
            }
        } catch (Exception e) {
            log.error("检查分片大小失败: {}", shardKey, e);
        }
    }

    /**
     * 获取分片数量
     *
     * @return 分片数量
     */
    public int getShardCount() {
        return chargeBillingProperties.getShard().getCount();
    }

    /**
     * 获取每个分片的最大大小
     *
     * @return 最大分片大小
     */
    public int getMaxShardSize() {
        return chargeBillingProperties.getShard().getMaxSize();
    }

    /**
     * 获取过期时间阈值（小时）
     *
     * @return 过期时间阈值
     */
    public int getExpireHours() {
        return chargeBillingProperties.getShard().getExpireHours();
    }
}
