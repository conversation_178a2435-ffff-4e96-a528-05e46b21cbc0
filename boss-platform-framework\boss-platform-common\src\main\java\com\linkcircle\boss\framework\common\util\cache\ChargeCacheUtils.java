package com.linkcircle.boss.framework.common.util.cache;

import cn.hutool.core.text.StrFormatter;
import com.linkcircle.boss.framework.common.constants.ChargeCacheConstant;

/**
 * <AUTHOR>
 * @date 2025-06-13 14:53
 * @description 计费缓存工具类
 */
public class ChargeCacheUtils {

    /**
     * 获取请求ID唯一性缓存key
     *
     * @param requestId 请求ID
     * @return 缓存key
     */
    public static String getRequestIdKey(String requestId) {
        return StrFormatter.format(ChargeCacheConstant.REQUEST_ID_PREFIX, requestId);
    }

    /**
     * 获取业务ID唯一性缓存key
     *
     * @param businessId 业务ID
     * @return 缓存key
     */
    public static String getBusinessIdKey(String businessId) {
        return StrFormatter.format(ChargeCacheConstant.BUSINESS_ID_PREFIX, businessId);
    }

    /**
     * 获取账户订阅信息缓存key
     *
     * @param accountId 账户ID
     * @return 缓存key
     */
    public static String getAccountSubscriptionsKey(Long accountId) {
        return StrFormatter.format(ChargeCacheConstant.ACCOUNT_SUBSCRIPTIONS_PREFIX, accountId);
    }

    /**
     * 获取订阅信息详情缓存key
     *
     * @param subscriptionId 订阅ID
     * @return 缓存key
     */
    public static String getSubscriptionsDetailKey(Long subscriptionId) {
        return StrFormatter.format(ChargeCacheConstant.SUBSCRIPTION_DETAIL_PREFIX, subscriptionId);
    }

    /**
     * 获取供应商资源采购信息缓存key
     *
     * @param accountId 供应商账户ID
     * @return 缓存key
     */
    public static String getAccountResourcePurchasesKey(Long accountId) {
        return StrFormatter.format(ChargeCacheConstant.ACCOUNT_RESOURCE_PURCHASES_PREFIX, accountId);
    }

    /**
     * 获取资源采购信息详情缓存key
     *
     * @param purchaseId 供应商资源采购信息ID
     * @return 缓存key
     */
    public static String getResourcePurchaseDetailKey(Long purchaseId) {
        return StrFormatter.format(ChargeCacheConstant.RESOURCE_PURCHASE_DETAIL_PREFIX, purchaseId);
    }

    /**
     * 获取账单ID序号缓存key
     *
     * @param type    id类型
     * @param dateStr 日期字符串 yyyyMMdd
     * @return 缓存key
     */
    public static String getBillIdSequenceKey(String type, String dateStr) {
        return StrFormatter.format(ChargeCacheConstant.BILL_ID_SEQUENCE_PREFIX, type, dateStr);
    }

    /**
     * 获取客户账户信息缓存key
     *
     * @param accountId 账户ID
     * @return 缓存key
     */
    public static String getCustomerAccountKey(Long accountId) {
        return StrFormatter.format(ChargeCacheConstant.CUSTOMER_ACCOUNT_PREFIX, accountId);
    }

    /**
     * 获取供应商账户信息缓存key
     *
     * @param accountId 账户ID
     * @return 缓存key
     */
    public static String getSupplierAccountKey(Long accountId) {
        return StrFormatter.format(ChargeCacheConstant.SUPPLIER_ACCOUNT_PREFIX, accountId);
    }

    /**
     * 获取费率计费缓存key
     *
     * @param billType     账单类型：income/cost
     * @param rateType     费率类型：fixed/tiered/package/usage
     * @param dimensionKey 维度key：订阅ID 或 账户ID:服务ID
     * @param billingCycle 计费周期
     * @return 缓存key
     */
    public static String getRateBillingKey(String billType, String rateType, String dimensionKey, String billingCycle) {
        return StrFormatter.format(ChargeCacheConstant.RATE_BILLING_PREFIX, billType, rateType, dimensionKey, billingCycle);
    }

    /**
     * 获取量表字段配置信息缓存key
     *
     * @param scaleId 量表ID
     * @return 缓存key
     */
    public static String getScaleColumnsKey(Long scaleId) {
        return StrFormatter.format(ChargeCacheConstant.SCALE_COLUMNS_PREFIX, scaleId);
    }

    /**
     * 获取指标单位配置简单列表缓存key
     *
     * @return 缓存key
     */
    public static String getMetricUnitConfigSimpleListKey() {
        return ChargeCacheConstant.METRIC_UNIT_CONFIG_SIMPLE_LIST;
    }

    /**
     * 获取账户分布式锁key
     *
     * @param accountId 订阅ID
     * @return 锁key
     */
    public static String getAccountProcessLockKey(Long accountId) {
        return StrFormatter.format(ChargeCacheConstant.ACCOUNT_PROCESS_LOCK_PREFIX, accountId);
    }

    /**
     * 获取处理分布式锁key
     *
     * @param id id
     * @return 锁key
     */
    public static String getProcessLockKey(String type, String id) {
        return StrFormatter.format(ChargeCacheConstant.PROCESS_LOCK_PREFIX, type, id);
    }

    /**
     * 获取账户支付订单锁缓存 key
     *
     * @param accountId 上锁的账户 ID
     * @return 锁key
     */
    public static String getConsumerPaymentLockKey(Long accountId) {
        return StrFormatter.format(ChargeCacheConstant.CONSUMER_PAYMENT_ORDER_LOCK_PREFIX, accountId);
    }

    public static String getDownloadTaskIncrement(String key) {
        return StrFormatter.format(ChargeCacheConstant.CONSUMER_DOWNLOAD_TASK_INCREMENT_PREFIX, key);
    }

    /**
     * 服务账单是否出账完成
     *
     * @param serviceId      服务编码
     * @param subscriptionId 订阅ID
     * @param cycleStartTime 周期开始时间
     * @param cycleEndTime   周期结束时间
     * @return 缓存key
     */
    public static String getServiceBillCacheKey(Long serviceId, Long subscriptionId, Long cycleStartTime, Long cycleEndTime) {
        return StrFormatter.format(ChargeCacheConstant.SERVICE_BILL_CACHE_PREFIX, serviceId, subscriptionId, cycleStartTime, cycleEndTime);
    }

    /**
     * 产品账单是否出账完成
     *
     * @param productId      产品ID
     * @param subscriptionId 订阅ID
     * @param cycleStartTime 周期开始时间
     * @param cycleEndTime   周期结束时间
     * @return 缓存key
     */
    public static String getProductBillCacheKey(Long productId, Long subscriptionId, Long cycleStartTime, Long cycleEndTime) {
        return StrFormatter.format(ChargeCacheConstant.PRODUCT_BILL_CACHE_PREFIX, productId, subscriptionId, cycleStartTime, cycleEndTime);
    }

    /**
     * 资源服务账单是否出账完成
     *
     * @param serviceCode    服务编码
     * @param purchaseId     采购ID
     * @param cycleStartTime 周期开始时间
     * @param cycleEndTime   周期结束时间
     * @return 缓存key
     */
    public static String getResourceServiceBillCacheKey(String serviceCode, Long purchaseId, Long cycleStartTime, Long cycleEndTime) {
        return StrFormatter.format(ChargeCacheConstant.RESOURCE_SERVICE_BILL_CACHE_PREFIX, serviceCode, purchaseId, cycleStartTime, cycleEndTime);
    }

    /**
     * 获取计费防重复处理锁key
     *
     * @param rateType       费率类型
     * @param subscriptionId 订阅ID
     * @param serviceId      服务ID
     * @param billingCycle   计费周期
     * @return 缓存key
     */
    public static String getBillingPreventDuplicateKey(String rateType, Long subscriptionId, Long serviceId, String billingCycle) {
        return StrFormatter.format(ChargeCacheConstant.BILLING_PREVENT_DUPLICATE_PREFIX, rateType, subscriptionId, serviceId, billingCycle);
    }

    /**
     * 获取费率用量缓存key
     *
     * @param rateType       费率类型 (package/usage)
     * @param serviceId      服务ID
     * @param subscriptionId 订阅ID
     * @param billingCycle   计费周期
     * @return 缓存key
     */
    public static String getRateUsageCacheKey(String rateType, Long serviceId, Long subscriptionId, String billingCycle) {
        return StrFormatter.format(ChargeCacheConstant.RATE_USAGE_CACHE_PREFIX, rateType, serviceId, subscriptionId, billingCycle);
    }

    /**
     * 获取统一费率用量缓存key
     *
     * @param billType     账单类型 (income/cost)
     * @param rateType     费率类型 (package/usage)
     * @param serviceId    服务ID
     * @param entityId     实体ID (收入用subscriptionId，成本用purchaseId)
     * @param billingCycle 计费周期
     * @return 缓存key
     */
    public static String getUnifiedRateUsageCacheKey(String billType, String rateType, Long serviceId, Long entityId, String billingCycle) {
        return StrFormatter.format(ChargeCacheConstant.UNIFIED_RATE_USAGE_CACHE_PREFIX, billType, rateType, serviceId, entityId, billingCycle);
    }

    /**
     * 获取费率用量同步分片key
     *
     * @param shardIndex 分片索引
     * @return ZSet分片key
     */
    public static String getRateUsageSyncShardKey(int shardIndex) {
        return StrFormatter.format(ChargeCacheConstant.RATE_USAGE_SYNC_SHARD_PREFIX, shardIndex);
    }
}
