package com.linkcircle.boss.module.billing.web.detail.cost.service.calculation.strategy.impl;

import com.linkcircle.boss.framework.common.exception.util.ServiceExceptionUtil;
import com.linkcircle.boss.module.billing.api.rate.model.dto.TierRateConfigDTO;
import com.linkcircle.boss.module.billing.constants.ErrorCodeConstants;
import com.linkcircle.boss.module.billing.constants.RateTypeConstant;
import com.linkcircle.boss.module.billing.enums.CostRateTypeEnum;
import com.linkcircle.boss.module.billing.enums.OriginalPriceRateTypeEnum;
import com.linkcircle.boss.module.billing.exception.RepeatedBillingException;
import com.linkcircle.boss.module.billing.web.data.model.vo.CyclePeriodResultVO;
import com.linkcircle.boss.module.billing.web.data.service.CyclePeriodCalculateService;
import com.linkcircle.boss.module.billing.web.detail.cost.model.dto.ResourceSubscriptionInfoDTO;
import com.linkcircle.boss.module.billing.web.detail.cost.service.calculation.strategy.AbstractCostRateChargeStrategy;
import com.linkcircle.boss.module.billing.web.detail.cost.service.calculation.strategy.context.CostRateChargeRequest;
import com.linkcircle.boss.module.billing.web.detail.cost.service.calculation.strategy.context.CostRateChargeResponse;
import com.linkcircle.boss.module.billing.web.detail.rate.cost.model.vo.RateUsageVO;
import com.linkcircle.boss.module.billing.web.detail.rate.cost.service.CostRateUsageManageService;
import com.linkcircle.boss.module.billing.web.detail.rate.strategy.context.OriginalPriceCalculateRequest;
import com.linkcircle.boss.module.billing.web.detail.rate.strategy.context.OriginalPriceCalculateResponse;
import com.linkcircle.boss.module.crm.api.supplier.account.vo.SupplierAccountVO;
import com.linkcircle.boss.module.crm.api.supplier.purchase.vo.ResourcePurchaseVO;
import com.linkcircle.boss.module.crm.enums.ChargeRateTypeEnum;
import io.github.kk01001.design.pattern.strategy.IStrategy;
import io.github.kk01001.design.pattern.strategy.StrategyFactory;
import io.github.kk01001.design.pattern.strategy.annotation.Strategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-06-24 16:10
 * @description 成本阶梯费率计费策略
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Strategy(strategyEnum = CostRateTypeEnum.class, strategyType = RateTypeConstant.TIERED)
public class CostTieredRateChargeStrategy extends AbstractCostRateChargeStrategy implements IStrategy<CostRateChargeRequest, CostRateChargeResponse> {

    private final CyclePeriodCalculateService cyclePeriodCalculateService;
    private final CostRateUsageManageService costRateUsageManageService;
    private final StrategyFactory strategyFactory;

    @Override
    protected CyclePeriodCalculateService getCyclePeriodCalculateService() {
        return cyclePeriodCalculateService;
    }

    @Override
    public CostRateChargeResponse execute(CostRateChargeRequest request) {
        log.info("开始执行成本阶梯费率计费策略");

        // 1. 获取基础数据
        ResourceSubscriptionInfoDTO resourceInfo = request.getResourceSubscriptionInfoDTO();
        ResourcePurchaseVO purchaseVO = resourceInfo.getPurchaseVO();
        ResourcePurchaseVO.Detail detail = resourceInfo.getDetail();
        BigDecimal currentUsage = request.getUsage();
        SupplierAccountVO supplierAccountVO = request.getSupplierAccountVO();
        String accountCurrency = supplierAccountVO.getCurrency();

        // 2. 解析阶梯费率配置
        TierRateConfigDTO rateConfig = getTierRateConfigDTO(detail.getCurrencyPriceJson(), accountCurrency);

        // 4. 计算计费周期
        Long businessTime = request.getBusinessTime();
        CyclePeriodResultVO cyclePeriodResultVO = getBillingCycle(resourceInfo, supplierAccountVO, businessTime);
        // 5. 获取当前周期累计用量
        RateUsageVO rateUsageVO = costRateUsageManageService.getRateUsage(ChargeRateTypeEnum.TIERED, cyclePeriodResultVO);
        if (Boolean.TRUE.equals(rateUsageVO.getExist())) {
            log.error("阶梯费率计费锁定失败，可能已计费");
            throw new RepeatedBillingException(ErrorCodeConstants.BILLING_CALCULATION_FAILED, "阶梯费率已计费");
        }

        BigDecimal previousUsage = rateUsageVO.getUsageCount();
        BigDecimal totalUsageWithCurrent = previousUsage.add(currentUsage);

        // 6. 计算原价
        OriginalPriceCalculateResponse calculateResponse = calculateOriginalPriceByStrategy(purchaseVO, totalUsageWithCurrent,
                previousUsage, currentUsage, rateConfig, detail.getPaymentOptions());
        BigDecimal originalPrice = calculateResponse.getOriginalPrice();

        // 8. db更新费率用量
        costRateUsageManageService.updateRateUsage(cyclePeriodResultVO, currentUsage, request.getUsageUnit(), rateUsageVO.getExist());

        // 9. 构建响应结果
        CostRateChargeResponse response = CostRateChargeResponse.success();
        response.setCurrency(accountCurrency);
        response.setUsage(currentUsage);
        response.setUsageUnit(request.getUsageUnit());
        response.setCyclePeriodResultVO(cyclePeriodResultVO);
        response.setRateConfig(rateConfig);
        convertChargeResponse(response, calculateResponse);

        log.info("成本阶梯费率计费策略执行完成，原价: {}, 累计用量: {}", originalPrice, totalUsageWithCurrent);
        return response;
    }

    /**
     * 通过策略计算阶梯费率目录价
     *
     * @param totalUsageWithCurrent 包含本次用量的总累计用量
     * @param previousUsage         之前的累计用量
     * @param currentUsage          本次用量
     * @param rateConfig            阶梯费率配置
     * @param paymentOptions        支付方式 0-现金 1-积分
     * @return 本次计费金额
     */
    private OriginalPriceCalculateResponse calculateOriginalPriceByStrategy(ResourcePurchaseVO purchaseVO,
                                                                            BigDecimal totalUsageWithCurrent,
                                                                            BigDecimal previousUsage,
                                                                            BigDecimal currentUsage,
                                                                            TierRateConfigDTO rateConfig,
                                                                            Integer paymentOptions) {
        // 构建原价计算请求
        OriginalPriceCalculateRequest request = new OriginalPriceCalculateRequest();
        request.setTotalUsageWithCurrent(totalUsageWithCurrent);
        request.setPreviousUsage(previousUsage);
        request.setCurrentUsage(currentUsage);
        request.setRateConfig(rateConfig);
        request.setPaymentOptions(paymentOptions);
        request.setCalculateTaxEnabled(purchaseVO.getIsTaxInclusive());
        request.setTaxRate(purchaseVO.getRate());

        // 调用原价计算策略
        OriginalPriceCalculateResponse response = strategyFactory.execute(OriginalPriceRateTypeEnum.class,
                OriginalPriceRateTypeEnum.TIERED.name(), request);

        if (response.getSuccess()) {
            return response;
        }
        throw ServiceExceptionUtil.exception(ErrorCodeConstants.BILLING_CALCULATION_FAILED, response.getErrorMessage());
    }
}
