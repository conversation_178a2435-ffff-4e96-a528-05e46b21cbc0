package com.linkcircle.boss.module.charge.fee.web.bill.controller;

import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.framework.permission.PreAuthorize;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.BillReqDto;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.BillReqHistoryDto;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.BillSendEmailReqDto;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.calculate.BillFeeCalculateReqDTO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.makeup.create.MakeupBillCreateReqDTO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.makeup.update.MakeupBillUpdateReqDTO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.calculate.BillFeeResponseVO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.history.IncomeBillHistoryVO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.makeup.MakeupIncomeBillDetailVo;
import com.linkcircle.boss.module.charge.fee.web.bill.service.BillFeeService;
import com.linkcircle.boss.module.charge.fee.web.bill.service.MakeupBillService;
import com.linkcircle.boss.module.charge.fee.web.invoiceRecord.model.dto.InvoiceSendMailDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.ValidationException;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.linkcircle.boss.framework.common.model.CommonResult.success;

/**
 * <AUTHOR>
 * @date 2025/7/1 19:22
 */
@Tag(name = "账单-手工账单")
@RestController
@RequestMapping("/customer/makeup-bill")
@Validated
@RequiredArgsConstructor
public class MakeupBillController {


    private final MakeupBillService makeupBillService;
    private final BillFeeService billFeeService;


    /**
     * 发起手工账单结清
     * 该接口用于用户发起手工账单的结清操作，通过传入账单请求信息来完成结清流程。
     *
     * @param reqDto 账单请求信息，包含账单的相关数据
     * @return 返回操作结果，成功则返回成功信息
     * @throws ValidationException 如果请求参数不符合校验规则，则抛出ValidationException异常
     */
    @PostMapping("/payOff")
    @Operation(summary = "发起手工账单结清")
    @PreAuthorize("@ss.hasPermission('customer:makeup-bill:pay-off')")
    public CommonResult<String> payOffBill(@Valid @RequestBody BillReqDto reqDto) {
        makeupBillService.payOffBill(reqDto);
        return success();
    }

    /**
     * 手工账单确认接口
     * 该接口用于手工确认账单费用，通过传入账单计算请求数据进行确认操作。
     *
     * @param updateReqDTO 账单计算请求数据，包含账单相关的详细信息
     * @return CommonResult<BillFeeResponseVO> 返回操作结果，包含账单确认后的响应数据
     */
    @PostMapping("/make-sure")
    @Operation(summary = "手工账单确认")
    @PreAuthorize("@ss.hasPermission('customer:makeup-bill:make-sure')")
    public CommonResult<BillFeeResponseVO> makeSure(@Valid @RequestBody BillFeeCalculateReqDTO updateReqDTO) {
        return success(billFeeService.makeSureBill(updateReqDTO));
    }

    /**
     * 手工账单创建接口
     * 该接口用于创建手工账单，通过传入账单计算请求参数来实现账单的生成。
     *
     * @param createReqDTO 账单计算请求参数，包含创建账单所需的各种信息
     * @return CommonResult<BillFeeResponseVO> 返回账单创建的结果，包含账单费用响应信息
     */
    @PostMapping("/make-up")
    @Operation(summary = "手工账单-账单创建")
    @PreAuthorize("@ss.hasPermission('customer:makeup-bill:make-up')")
    public CommonResult<BillFeeResponseVO> makeup(@Valid @RequestBody BillFeeCalculateReqDTO createReqDTO) {
        return success(billFeeService.createBill(createReqDTO));
    }



    /**
     * 手工账单有确认退回为草稿状态接口
     * 该接口用于将已确认的手工账单退回至草稿状态
     *
     * @param reqDto 请求参数，包含账单的相关信息
     * @return 操作成功返回的结果
     */
    @PostMapping("/to-draft")
    @Operation(summary = "手工账单有确认退回为草稿状态")
    @PreAuthorize("@ss.hasPermission('customer:makeup-bill:to-draft')")
    public CommonResult<String> toDraft(@Valid @RequestBody BillReqDto reqDto) {
        makeupBillService.toDraft(reqDto);
        return success();
    }


    @PostMapping("/history-list")
    @Operation(summary = "手工账单历史记录")
    @PreAuthorize("@ss.hasPermission('customer:makeup-bill:history')")
    public CommonResult<PageResult<IncomeBillHistoryVO<MakeupIncomeBillDetailVo>>> historyBills(@Valid @RequestBody BillReqHistoryDto reqDto) {
        return success(makeupBillService.historyBills(reqDto));
    }


    @PostMapping("/sendMail")
    @Operation(summary = "发送邮箱")
    @PreAuthorize("@ss.hasPermission('customer:makeup-bill:send-email')")
    public CommonResult<Boolean> sendMail(@RequestBody BillSendEmailReqDto dto) {
        Boolean vo = makeupBillService.sendMail(dto);
        return CommonResult.success(vo);
    }


    @PostMapping("/delete")
    @Operation(summary = "删除手工账单")
    @PreAuthorize("@ss.hasPermission('customer:makeup-bill:delete')")
    public CommonResult<String> deleteBill(@Valid@RequestBody BillReqDto reqDto) {
        makeupBillService.deleteBill(reqDto);
        return success();
    }
}
