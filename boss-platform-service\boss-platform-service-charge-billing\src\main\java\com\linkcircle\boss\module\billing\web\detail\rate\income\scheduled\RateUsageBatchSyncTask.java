package com.linkcircle.boss.module.billing.web.detail.rate.income.scheduled;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.linkcircle.boss.framework.common.util.cache.ChargeCacheUtils;
import com.linkcircle.boss.framework.web.context.EnableLoginContext;
import com.linkcircle.boss.module.billing.web.detail.rate.income.mapper.IncomePackageRateUsageMapper;
import com.linkcircle.boss.module.billing.web.detail.rate.income.mapper.IncomeUsageRateUsageMapper;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.github.kk01001.redis.RedissonUtil;
import io.github.kk01001.util.TraceIdUtil;
import io.github.kk01001.xxljob.annotations.XxlJobRegister;
import io.github.kk01001.xxljob.enums.ExecutorRouteStrategyEnum;
import io.github.kk01001.xxljob.enums.MisfireStrategyEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025-08-05 16:21
 * @description 费率用量批量同步定时任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RateUsageBatchSyncTask {

    private final RedissonUtil redissonUtil;
    private final IncomePackageRateUsageMapper incomePackageRateUsageMapper;
    private final IncomeUsageRateUsageMapper incomeUsageRateUsageMapper;

    @XxlJob("rateUsageBatchSyncHandler")
    @XxlJobRegister(
            cron = "0 */5 * * * ?", // 每5分钟执行一次
            jobDesc = "费率用量批量同步任务",
            author = "linshiqiang",
            triggerStatus = 1,
            executorRouteStrategy = ExecutorRouteStrategyEnum.ROUND,
            misfireStrategy = MisfireStrategyEnum.DO_NOTHING
    )
    public void rateUsageBatchSyncHandler() {
        String traceId = TraceIdUtil.generateTraceId();
        TraceIdUtil.setTraceId(traceId);
        
        try {
            log.info("开始执行费率用量批量同步任务");
            
            // 设置登录用户上下文
            EnableLoginContext.setLoginUserId(1L);
            EnableLoginContext.setLoginUserType(1);

            // 批量同步套餐费率用量
            syncPackageRateUsage();
            
            // 批量同步按量费率用量
            syncUsageRateUsage();
            
            log.info("费率用量批量同步任务执行完成");
            
        } catch (Exception e) {
            log.error("费率用量批量同步任务执行异常", e);
        } finally {
            // 清理上下文
            EnableLoginContext.clear();
            TraceIdUtil.clear();
        }
    }

    /**
     * 批量同步套餐费率用量
     */
    private void syncPackageRateUsage() {
        try {
            String pattern = "boss:charge:billing:usage:package:*";
            Set<String> keys = redissonUtil.keys(pattern);
            
            if (CollUtil.isEmpty(keys)) {
                log.debug("没有找到套餐费率用量缓存数据");
                return;
            }
            
            log.info("开始批量同步套餐费率用量，共{}条记录", keys.size());
            int successCount = 0;
            int failCount = 0;
            
            for (String key : keys) {
                try {
                    if (syncSinglePackageRateUsage(key)) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                } catch (Exception e) {
                    failCount++;
                    log.error("同步单个套餐费率用量失败, key: {}", key, e);
                }
            }
            
            log.info("套餐费率用量批量同步完成，成功: {}, 失败: {}", successCount, failCount);
            
        } catch (Exception e) {
            log.error("批量同步套餐费率用量异常", e);
        }
    }

    /**
     * 批量同步按量费率用量
     */
    private void syncUsageRateUsage() {
        try {
            String pattern = "boss:charge:billing:usage:usage:*";
            Set<String> keys = redissonUtil.keys(pattern);
            
            if (CollUtil.isEmpty(keys)) {
                log.debug("没有找到按量费率用量缓存数据");
                return;
            }
            
            log.info("开始批量同步按量费率用量，共{}条记录", keys.size());
            int successCount = 0;
            int failCount = 0;
            
            for (String key : keys) {
                try {
                    if (syncSingleUsageRateUsage(key)) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                } catch (Exception e) {
                    failCount++;
                    log.error("同步单个按量费率用量失败, key: {}", key, e);
                }
            }
            
            log.info("按量费率用量批量同步完成，成功: {}, 失败: {}", successCount, failCount);
            
        } catch (Exception e) {
            log.error("批量同步按量费率用量异常", e);
        }
    }

    /**
     * 同步单个套餐费率用量
     */
    private boolean syncSinglePackageRateUsage(String cacheKey) {
        try {
            // 解析key获取参数
            String[] parts = cacheKey.split(":");
            if (parts.length != 8) {
                log.warn("套餐费率用量缓存key格式不正确: {}", cacheKey);
                return false;
            }
            
            Long serviceId = Long.valueOf(parts[5]);
            Long subscriptionId = Long.valueOf(parts[6]);
            String billingCycle = parts[7];
            
            // 获取缓存数据
            String totalUsageStr = redissonUtil.hget(cacheKey, "total_usage");
            String billingTimeStr = redissonUtil.hget(cacheKey, "billing_time");
            String usageUnit = redissonUtil.hget(cacheKey, "usage_unit");
            String currency = redissonUtil.hget(cacheKey, "currency");
            
            if (StrUtil.isBlank(totalUsageStr)) {
                log.warn("套餐费率用量缓存数据不完整: {}", cacheKey);
                return false;
            }
            
            BigDecimal totalUsage = new BigDecimal(totalUsageStr);
            Long billingTime = StrUtil.isNotBlank(billingTimeStr) ? Long.valueOf(billingTimeStr) : System.currentTimeMillis();
            
            // 更新数据库（这里需要实现upsert逻辑）
            // TODO: 实现数据库的插入或更新逻辑
            log.debug("同步套餐费率用量到数据库: serviceId={}, subscriptionId={}, billingCycle={}, totalUsage={}", 
                    serviceId, subscriptionId, billingCycle, totalUsage);
            
            return true;
            
        } catch (Exception e) {
            log.error("同步单个套餐费率用量异常, cacheKey: {}", cacheKey, e);
            return false;
        }
    }

    /**
     * 同步单个按量费率用量
     */
    private boolean syncSingleUsageRateUsage(String cacheKey) {
        try {
            // 解析key获取参数
            String[] parts = cacheKey.split(":");
            if (parts.length != 8) {
                log.warn("按量费率用量缓存key格式不正确: {}", cacheKey);
                return false;
            }
            
            Long serviceId = Long.valueOf(parts[5]);
            Long subscriptionId = Long.valueOf(parts[6]);
            String billingCycle = parts[7];
            
            // 获取缓存数据
            String totalUsageStr = redissonUtil.hget(cacheKey, "total_usage");
            String billingTimeStr = redissonUtil.hget(cacheKey, "billing_time");
            String usageUnit = redissonUtil.hget(cacheKey, "usage_unit");
            String currency = redissonUtil.hget(cacheKey, "currency");
            
            if (StrUtil.isBlank(totalUsageStr)) {
                log.warn("按量费率用量缓存数据不完整: {}", cacheKey);
                return false;
            }
            
            BigDecimal totalUsage = new BigDecimal(totalUsageStr);
            Long billingTime = StrUtil.isNotBlank(billingTimeStr) ? Long.valueOf(billingTimeStr) : System.currentTimeMillis();
            
            // 更新数据库（这里需要实现upsert逻辑）
            // TODO: 实现数据库的插入或更新逻辑
            log.debug("同步按量费率用量到数据库: serviceId={}, subscriptionId={}, billingCycle={}, totalUsage={}", 
                    serviceId, subscriptionId, billingCycle, totalUsage);
            
            return true;
            
        } catch (Exception e) {
            log.error("同步单个按量费率用量异常, cacheKey: {}", cacheKey, e);
            return false;
        }
    }
}
