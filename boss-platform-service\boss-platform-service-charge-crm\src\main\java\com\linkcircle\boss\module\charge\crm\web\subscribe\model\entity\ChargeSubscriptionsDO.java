package com.linkcircle.boss.module.charge.crm.web.subscribe.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.linkcircle.boss.framework.tanant.TenantBaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 账户订阅信息表实体类
 *
 * <AUTHOR>
 */
@TableName("charge_subscriptions")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChargeSubscriptionsDO extends TenantBaseDO {
    /**
     * 订阅id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 账户ID
     */
    private Long accountId;

    /**
     * 钱包id
     */
    private Long walletsId;

    /**
     * 主体ID
     */
    private Long entityId;

    /**
     * 计划id
     */
    private Long planId;

    /**
     * 合同id
     */
    private Long contractId;

    /**
     * 税率百分比（示例：9.00）
     */
    private BigDecimal rate;

    /**
     * 支付类型，0：预付费，1后付费
     */
    private Integer paymentType;

    /**
     * 状态，0：试用中，1：已生效，2：逾期，3：未支付，4：已取消，5：已结束 枚举:ChargeSubscriptionStatusEnum
     */
    private Integer status;

    /**
     * 出账生成草稿，0：否，1：是
     */
    private Integer billDrafts;

    /**
     * 支付方式，0:现金，1：积分
     */
    private Integer paymentOptions;

    /**
     * 订阅时间详情json
     */
    private String subJson;

    /**
     * 审批流程状态
     */
    private Integer bpmStatus;

    /**
     * 流程实例的编号
     */
    private String processInstanceId;
}