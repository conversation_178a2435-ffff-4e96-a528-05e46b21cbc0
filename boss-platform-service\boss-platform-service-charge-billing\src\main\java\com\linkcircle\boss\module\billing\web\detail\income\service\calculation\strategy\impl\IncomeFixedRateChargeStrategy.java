package com.linkcircle.boss.module.billing.web.detail.income.service.calculation.strategy.impl;

import com.linkcircle.boss.framework.common.exception.util.ServiceExceptionUtil;
import com.linkcircle.boss.module.billing.api.rate.model.dto.FixedRateConfigDTO;
import com.linkcircle.boss.module.billing.constants.ErrorCodeConstants;
import com.linkcircle.boss.module.billing.constants.RateTypeConstant;
import com.linkcircle.boss.module.billing.enums.IncomeRateTypeEnum;
import com.linkcircle.boss.module.billing.enums.OriginalPriceRateTypeEnum;
import com.linkcircle.boss.module.billing.exception.RepeatedBillingException;
import com.linkcircle.boss.module.billing.web.data.model.vo.CyclePeriodResultVO;
import com.linkcircle.boss.module.billing.web.data.service.CyclePeriodCalculateService;
import com.linkcircle.boss.module.billing.web.detail.income.model.dto.ServiceSubscriptionInfoDTO;
import com.linkcircle.boss.module.billing.web.detail.income.service.calculation.strategy.AbstractIncomeRateChargeStrategy;
import com.linkcircle.boss.module.billing.web.detail.income.service.calculation.strategy.context.IncomeRateChargeRequest;
import com.linkcircle.boss.module.billing.web.detail.income.service.calculation.strategy.context.IncomeRateChargeResponse;
import com.linkcircle.boss.module.billing.web.detail.rate.cost.model.vo.RateUsageVO;
import com.linkcircle.boss.module.billing.web.detail.rate.income.service.IncomeRateUsageManageService;
import com.linkcircle.boss.module.billing.web.detail.rate.strategy.context.OriginalPriceCalculateRequest;
import com.linkcircle.boss.module.billing.web.detail.rate.strategy.context.OriginalPriceCalculateResponse;
import com.linkcircle.boss.module.crm.api.customer.account.vo.CustomerAccountVO;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.AccountSubscriptionsVO;
import com.linkcircle.boss.module.crm.enums.ChargeRateTypeEnum;
import io.github.kk01001.design.pattern.strategy.IStrategy;
import io.github.kk01001.design.pattern.strategy.StrategyFactory;
import io.github.kk01001.design.pattern.strategy.annotation.Strategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-06-19 15:16
 * @description 固定费率计费策略
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Strategy(strategyEnum = IncomeRateTypeEnum.class, strategyType = RateTypeConstant.FIXED)
public class IncomeFixedRateChargeStrategy extends AbstractIncomeRateChargeStrategy implements IStrategy<IncomeRateChargeRequest, IncomeRateChargeResponse> {

    private final CyclePeriodCalculateService cyclePeriodCalculateService;
    private final IncomeRateUsageManageService incomeRateUsageManageService;
    private final StrategyFactory strategyFactory;

    @Override
    protected CyclePeriodCalculateService getCyclePeriodCalculateService() {
        return cyclePeriodCalculateService;
    }

    @Override
    public IncomeRateChargeResponse execute(IncomeRateChargeRequest request) {
        log.info("开始执行固定费率计费策略");

        // 1. 获取基础数据
        ServiceSubscriptionInfoDTO serviceInfo = request.getServiceSubscriptionInfoDTO();
        CustomerAccountVO customerAccountVO = request.getCustomerAccountVO();
        String accountCurrency = customerAccountVO.getCurrency();
        AccountSubscriptionsVO.Service serviceConfig = serviceInfo.getServiceConfig();
        BigDecimal currentUsage = request.getUsage();

        // 2. 解析固定费率配置
        FixedRateConfigDTO rateConfig = getFixedRateConfigDTO(serviceConfig, accountCurrency);

        Long businessTime = request.getBusinessTime();
        CyclePeriodResultVO cyclePeriodResultVO = getBillingCycle(serviceInfo, customerAccountVO, businessTime);
        RateUsageVO rateUsageVO = incomeRateUsageManageService.getRateUsage(ChargeRateTypeEnum.FIXED, cyclePeriodResultVO);
        if (Boolean.TRUE.equals(rateUsageVO.getExist())) {
            log.error("固定费率计费锁定失败，可能已计费");
            throw new RepeatedBillingException(ErrorCodeConstants.BILLING_CALCULATION_FAILED, "固定费率已计费");
        }

        // 4. 计算目录单价 优惠单价
        BigDecimal taxRate = getTaxRate(serviceInfo);
        OriginalPriceCalculateResponse calculateResponse = calculateOriginalPriceByStrategy(rateConfig,
                serviceConfig.getPaymentOptions(), taxRate, request.enableTax());

        // 8. db更新费率用量
        incomeRateUsageManageService.updateRateUsage(cyclePeriodResultVO, currentUsage, request.getUsageUnit(), false, rateUsageVO.getExist());

        // 9. 构建响应结果
        IncomeRateChargeResponse response = IncomeRateChargeResponse.success();
        response.setSuccess(true);
        response.setCurrency(rateConfig.getCurrency());
        response.setUsage(currentUsage);
        response.setUsageUnit(request.getUsageUnit());
        response.setCyclePeriodResultVO(cyclePeriodResultVO);
        response.setRateConfig(rateConfig);
        response.setCouponList(serviceConfig.getCoupons());
        convertChargeResponse(response, calculateResponse);

        return response;
    }

    /**
     * 通过策略计算目录价
     */
    private OriginalPriceCalculateResponse calculateOriginalPriceByStrategy(FixedRateConfigDTO rateConfig,
                                                                            Integer paymentOptions,
                                                                            BigDecimal taxRate,
                                                                            boolean enableTax) {
        // 构建原价计算请求
        OriginalPriceCalculateRequest request = new OriginalPriceCalculateRequest();
        request.setRateConfig(rateConfig);
        request.setPaymentOptions(paymentOptions);
        request.setCurrentUsage(BigDecimal.ONE);
        request.setTaxRate(taxRate);
        request.setCalculateTaxEnabled(enableTax);

        // 调用原价计算策略
        OriginalPriceCalculateResponse response = strategyFactory.execute(OriginalPriceRateTypeEnum.class,
                OriginalPriceRateTypeEnum.FIXED.name(), request);

        if (response.getSuccess()) {
            return response;
        }
        throw ServiceExceptionUtil.exception(ErrorCodeConstants.BILLING_CALCULATION_FAILED, response.getErrorMessage());
    }

}
