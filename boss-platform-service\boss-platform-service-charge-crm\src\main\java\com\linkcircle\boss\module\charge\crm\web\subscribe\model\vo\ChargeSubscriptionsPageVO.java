package com.linkcircle.boss.module.charge.crm.web.subscribe.model.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.linkcircle.boss.framework.excel.core.annotations.DictFormat;
import com.linkcircle.boss.framework.excel.core.convert.DictConvert;
import com.linkcircle.boss.framework.excel.core.convert.TimestampConvert;
import com.linkcircle.boss.module.crm.constants.DictTypeConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 订阅分页返回结果 VO
 *
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "订阅分页返回结果")
public class ChargeSubscriptionsPageVO {

    @ExcelProperty(value = "订阅ID")
    @Schema(description = "订阅id")
    private String id;

    @Schema(description = "账户ID")
    private Long accountId;

    @ExcelProperty(value = "合同ID")
    @Schema(description = "合同编号")
    private String contractCode;

    @ExcelProperty(value = "项目ID")
    @Schema(description = "项目编号")
    private String projectCode;

    @Schema(description = "主体ID")
    private Long entityId;

    @ExcelProperty(value = "主体")
    @Schema(description = "主体名称")
    private String entityName;

    @ExcelProperty(value = "状态", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.SUBSCRIPTION_STATUS)
    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "客户ID")
    private Long customerId;

    @ExcelProperty(value = "客户/账户")
    @Schema(description = "客户/账户")
    private String clientAccount;

    @Schema(description = "计划id")
    private Long planId;

    @ExcelProperty(value = "订阅计划")
    @Schema(description = "计划名称")
    private String planName;

    @ExcelProperty(value = "持续时间(订阅开始时间)", converter = TimestampConvert.class)
    @Schema(description = "订阅开始时间")
    private Long startTime;

    @ExcelProperty(value = "持续时间(订阅结束时间)", converter = TimestampConvert.class)
    @Schema(description = "订阅结束时间")
    private Long endTime;

    @ExcelProperty(value = "下次出账时间", converter = TimestampConvert.class)
    @Schema(description = "下次出账时间")
    private Long nextBillTime;

    @ExcelProperty(value = "审核状态", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.BPM_TASK_STATUS)
    @Schema(description = "审核状态")
    private Integer bpmStatus;

    @Schema(description = "出账周期类型,0：每月，1：每周，2：每季度，3：每年")
    private Integer billingCycle;

    @Schema(description = "出账日 根据出账类型判断如 每月第3天、每周第一天(星期天)、每季度第五天")
    private Integer billingDay;

    @Schema(description = "时区")
    private String timezone;

    @ExcelProperty(value = "创建时间", converter = TimestampConvert.class)
    @Schema(description = "创建时间")
    private Long createTime;
}