package com.linkcircle.boss.module.charge.fee.web.payment.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class QueryPayOrderRQ {

    /**
     * 请求时间戳
     */
    @Schema(description = "请求时间戳")
    private String requestId;

    /**
     * 客户ID
     */
    @Schema(description = "商户ID")
    private String mchNo;

    /**
     * 支付系统订单号
     */
    @Schema(description = "支付系统订单号")
    private String payOrderId;

    /**
     * 服务商ID
     */
    @Schema(description = "服务商ID")
    private String isvId;

    /**
     * 请求时区,为数字0-23
     */
    @Schema(description = "请求时区")
    private String reqTimezone;

    /**
     * 签名值
     */
    @Schema(description = "签名值")
    private String sign;

}
