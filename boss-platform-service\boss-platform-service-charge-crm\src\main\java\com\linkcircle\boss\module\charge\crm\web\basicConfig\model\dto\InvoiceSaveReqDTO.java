package com.linkcircle.boss.module.charge.crm.web.basicConfig.model.dto;

import com.linkcircle.boss.module.charge.crm.web.basicConfig.model.entity.ChargeBillingInvoiceDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR> zyuan
 * @data : 2025-06-20
 */
@Schema(description = "管理后台 - 查看发票、账单配置 Request VO")
@Data
@EqualsAndHashCode(callSuper = false)
public class InvoiceSaveReqDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主体id
     */
    @Schema(description = "主体id（业务id）", requiredMode = Schema.RequiredMode.REQUIRED, example = "189009010")
    private Long entityId;

    /**
     * 号码格式
     */
    @Schema(description = "号码格式(账单、发票)", requiredMode = Schema.RequiredMode.REQUIRED, example = "Bill{YYYY}{MM}{DD}{number_4}")
    private String numberFormat;

    /**
     * 开始序列
     */
    @Schema(description = "开始序列", requiredMode = Schema.RequiredMode.REQUIRED, example = "3")
    private String startSequence;

    /**
     * 号码初始化：0-年 1-月 2-日
     */
    @Schema(description = "号码初始化：0-年 1-月 2-日", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Integer numberInit;

    /**
     * 品牌色
     */
    @Schema(description = "品牌色", requiredMode = Schema.RequiredMode.REQUIRED, example = "#05683C")
    private String brandColor;

    /**
     * 法律信息
     */
    @Schema(description = "法律信息", requiredMode = Schema.RequiredMode.REQUIRED, example = "xxx")
    private String legalInformation;

    /**
     * 附加信息
     */
    @Schema(description = "附加信息", requiredMode = Schema.RequiredMode.REQUIRED, example = "xxx")
    private String additionalInformation;

    /**
     * 配置类型 0-发票 1-账单
     */
    @Schema(description = "配置类型 （0-发票 1-账单)", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Integer type;

    public ChargeBillingInvoiceDO toEntity(ChargeBillingInvoiceDO entity) {
        if (entity == null) {
            entity = new ChargeBillingInvoiceDO();
        }
        if (this.entityId != null) {
            entity.setEntityId(this.entityId);
        }
        if (!StringUtils.isEmpty(this.numberFormat)) {
            entity.setNumberFormat(this.numberFormat);
        }
        if (!StringUtils.isEmpty(this.startSequence)) {
            entity.setStartSequence(this.startSequence);
        }
        if (this.numberInit != null) {
            entity.setNumberInit(this.numberInit);
        }
        if (!StringUtils.isEmpty(this.brandColor)) {
            entity.setBrandColor(this.brandColor);
        }
        if (!StringUtils.isEmpty(this.legalInformation)) {
            entity.setLegalInformation(this.legalInformation);
        }
        if (!StringUtils.isEmpty(this.additionalInformation)) {
            entity.setAdditionalInformation(this.additionalInformation);
        }
        if (this.type != null) {
            entity.setType(this.type);
        }
        return entity;
    }
}
