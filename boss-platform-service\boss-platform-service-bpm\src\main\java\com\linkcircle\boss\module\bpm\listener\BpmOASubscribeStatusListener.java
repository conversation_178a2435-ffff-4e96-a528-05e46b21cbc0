package com.linkcircle.boss.module.bpm.listener;

import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.module.bpm.enums.OAProcessKey;
import com.linkcircle.boss.module.bpm.enums.task.BpmTaskStatusEnum;
import com.linkcircle.boss.module.bpm.event.BpmProcessInstanceStatusEvent;
import com.linkcircle.boss.module.bpm.event.BpmProcessInstanceStatusEventListener;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.ChargeSubscriptionsApi;
import com.linkcircle.boss.module.crm.api.customer.subscriptions.vo.SubscribeResponseVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * OA 订阅的结果的监听器实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class BpmOASubscribeStatusListener extends BpmProcessInstanceStatusEventListener {

    @Resource
    private ChargeSubscriptionsApi subscriptionsApi;

    @Override
    protected String getProcessDefinitionKey() {
        return OAProcessKey.SUBSCRIBE_PROCESS_KEY;
    }

    @Override
    protected void onEvent(BpmProcessInstanceStatusEvent event) {
        long id = Long.parseLong(event.getBusinessKey());
        log.info("订阅OA流程实例id {}, 流程实例的 key: {}, 业务标识： {}, 流程实例的状态: {}",
                event.getId(), id, id, event.getStatus());
        CommonResult<SubscribeResponseVO> bySubsId = subscriptionsApi.getBaseInfoBySubsId(id);
        SubscribeResponseVO data = bySubsId.getCheckedData();
        // 审批通过的订阅,不需要更新审批状态,防止更新订阅重新审批影响原先审批通过的记录
        if (BpmTaskStatusEnum.APPROVE.getStatus().equals(data.getBpmStatus())) return;
        subscriptionsApi.changeSubscriptionBpmStatus(id, event.getStatus());
    }

}
