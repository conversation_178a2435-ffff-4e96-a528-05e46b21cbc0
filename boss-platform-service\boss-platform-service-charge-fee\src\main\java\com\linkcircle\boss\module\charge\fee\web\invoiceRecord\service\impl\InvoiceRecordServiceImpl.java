package com.linkcircle.boss.module.charge.fee.web.invoiceRecord.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linkcircle.boss.framework.common.exception.util.ServiceExceptionUtil;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.framework.mybatis.core.util.MyBatisUtils;
import com.linkcircle.boss.framework.web.context.LoginUser;
import com.linkcircle.boss.framework.web.core.util.WebFrameworkUtils;
import com.linkcircle.boss.module.charge.fee.constants.ErrorCodeConstants;
import com.linkcircle.boss.module.charge.fee.enums.InvoiceEnum;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.InvoiceBillReqDto;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.BillContent;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.BillCoupon;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.makeup.MakeupIncomeBillDetailVo;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.postpaid.PostpaidIncomeProductBillDetailVO;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.prepaid.PrepaidIncomeBillDetailVO;
import com.linkcircle.boss.module.charge.fee.web.bill.service.InvoiceIdGenerator;
import com.linkcircle.boss.module.charge.fee.web.bill.service.MakeupBillService;
import com.linkcircle.boss.module.charge.fee.web.bill.service.PostpaidService;
import com.linkcircle.boss.module.charge.fee.web.bill.service.PrepaidService;
import com.linkcircle.boss.module.charge.fee.web.invoice.mapper.InvoiceMapper;
import com.linkcircle.boss.module.charge.fee.web.invoice.model.dto.BillInvoiceAmountCancelDTO;
import com.linkcircle.boss.module.charge.fee.web.invoice.model.entity.ChargeInvoiceDO;
import com.linkcircle.boss.module.charge.fee.web.invoiceRecord.model.dto.CreditInvoiceDTO;
import com.linkcircle.boss.module.charge.fee.web.invoiceRecord.model.dto.InvoiceRecordReqDTO;
import com.linkcircle.boss.module.charge.fee.web.invoiceRecord.model.dto.InvoiceSendMailDTO;
import com.linkcircle.boss.module.charge.fee.web.invoiceRecord.model.dto.InvoiceToExamineDTO;
import com.linkcircle.boss.module.charge.fee.web.invoiceRecord.model.vo.InvoiceDetailsResVO;
import com.linkcircle.boss.module.charge.fee.web.invoiceRecord.model.vo.InvoiceRecordResVO;
import com.linkcircle.boss.module.charge.fee.web.invoiceRecord.service.InvoiceRecordService;
import com.linkcircle.boss.module.charge.fee.web.invoiceRecord.util.InvoiceConvertPdfUtil;
import com.linkcircle.boss.module.charge.fee.web.invoiceRecord.util.Test;
import com.linkcircle.boss.module.crm.api.basicConfig.BasicConfigApi;
import com.linkcircle.boss.module.crm.api.basicConfig.vo.InvoiceDetailsVO;
import com.linkcircle.boss.module.crm.api.customer.customer.CustomerApi;
import com.linkcircle.boss.module.crm.api.customer.customer.vo.ChargeCustomerInfoVO;
import com.linkcircle.boss.module.crm.api.entity.EntityApi;
import com.linkcircle.boss.module.crm.api.entity.vo.EntityDetailsVO;
import com.linkcircle.boss.module.system.api.mail.MailSendApi;
import com.linkcircle.boss.module.system.api.mail.dto.MailSendAttachmentsDTO;
import com.linkcircle.boss.module.system.api.mail.dto.MailSendSingleToUserReqDTO;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> zyuan
 * @data : 2025-06-30
 */
@Slf4j
@Service
public class InvoiceRecordServiceImpl implements InvoiceRecordService {

    @Resource
    private InvoiceMapper invoiceMapper;
    @Resource
    private CustomerApi customerApi;
    @Resource
    private BasicConfigApi basicConfigApi;
    @Resource
    private EntityApi entityApi;
    @Resource
    private MailSendApi mailSendApi;

    @Resource
    private MakeupBillService makeupBillService;
    @Resource
    private PrepaidService prepaidService;

    @Resource
    private PostpaidService postpaidService;

    @Resource
    private InvoiceIdGenerator invoiceIdGenerator;

    @Override
    public PageResult<InvoiceRecordResVO> getPage(InvoiceRecordReqDTO queryDTO) {
        Page<?> page = MyBatisUtils.buildPage(queryDTO);
        List<InvoiceRecordResVO> list = invoiceMapper.queryByPage(page, queryDTO);
        for (InvoiceRecordResVO vo : list) {
            vo.setCompleteAmount(vo.getCurrencySymbol() + vo.getInvoiceAmount());
            if (!StringUtils.isEmpty(vo.getEntityJsonStr())) {
                JSONObject object = JSON.parseObject(vo.getEntityJsonStr());
                vo.setEntityName(object.getString("entityName"));
            }
        }
        return MyBatisUtils.convert2PageResult(page, list);
    }

    @Override
    public InvoiceDetailsResVO details(Long id) {
        InvoiceDetailsResVO vo = new InvoiceDetailsResVO();
        ChargeInvoiceDO invoiceDO = invoiceMapper.selectById(id);
        if (invoiceDO != null) {
            vo.setInvoiceId(invoiceDO.getInvoiceId());
            vo.setType(invoiceDO.getType());
            vo.setStatus(invoiceDO.getStatus());
            vo.setInvoiceBillingId(invoiceDO.getInvoiceBillingId());
            vo.setInvoiceDate(formatDateWithOrdinalNew(invoiceDO.getCreateTime()));

            // 获取发票配置
            CommonResult<InvoiceDetailsVO> invoice = basicConfigApi.queryInvoice(invoiceDO.getEntityId(), 0);
            if (invoice.isSuccess()) {
                InvoiceDetailsVO data = invoice.getData();
                vo.setInvoiceConfig(data);
            }

            // 获取主体信息
            CommonResult<EntityDetailsVO> entityResult = entityApi.findById(invoiceDO.getEntityId());
            if (entityResult.isSuccess()) {
                EntityDetailsVO data = entityResult.getData();
                vo.setEntityInfo(data);
            }

            // 获取客户信息
            CommonResult<ChargeCustomerInfoVO> commonResult = customerApi.findById(invoiceDO.getCustomerId());
            if (commonResult.isSuccess()) {
                ChargeCustomerInfoVO data = commonResult.getData();
                vo.setCustomerInfo(data);
            }

            // 获取账单信息 1-预付费账单，2-后付费账单,3-手工账单
            Integer billType = invoiceDO.getBillType();
            InvoiceBillReqDto billReqDto = new InvoiceBillReqDto();
            billReqDto.setBillId(Long.parseLong(invoiceDO.getInvoiceBillingId()));
            billReqDto.setBillingTime(invoiceDO.getBillTime());
            billReqDto.setServiceCode(invoiceDO.getBillServiceCode());
            billReqDto.setInvoiceId(invoiceDO.getId());
            log.info("请求获取账单详情，请求参数：{}", JSON.toJSONString(billReqDto));
            String amountSymbol = invoiceDO.getCurrencySymbol();
            if (InvoiceEnum.BillType.PREPAID.is(billType)) {
                PrepaidIncomeBillDetailVO billDetailVO = prepaidService.queryInvoiceBillDetail(billReqDto);
                vo.setShowContents(billDetailVO.getShowContents());
                vo.setBillType(InvoiceEnum.BillType.PREPAID.getCode());
                vo.setTaxAmount(vo.formatBigDecimal(amountSymbol, billDetailVO.getTaxAmount()));
                vo.setSubTotalAmount(vo.formatBigDecimal(amountSymbol, billDetailVO.getSubTotalAmount()));
                vo.setDiscountAmount(vo.formatBigDecimal(amountSymbol, billDetailVO.getDiscountAmount()));
                vo.setAmountWithTax(vo.formatBigDecimal(amountSymbol, billDetailVO.getAmountWithTax()));
                vo.setAmountWithoutTax(vo.formatBigDecimal(amountSymbol, billDetailVO.getAmountWithoutTax()));
                vo.setBillingStartTime(billDetailVO.getBillingStartTime());
                vo.setBillingEndTime(billDetailVO.getBillingEndTime());
                vo.setShowCoupons(billDetailVO.getShowCoupons());
            } else if (InvoiceEnum.BillType.POSTPAID.is(billType)) {
                PostpaidIncomeProductBillDetailVO billDetailVO = postpaidService.queryInvoiceBillDetail(billReqDto);
                vo.setShowContents(billDetailVO.getShowContents());
                vo.setBillType(InvoiceEnum.BillType.POSTPAID.getCode());
                vo.setTaxAmount(vo.formatBigDecimal(amountSymbol, billDetailVO.getTaxAmount()));
                vo.setSubTotalAmount(vo.formatBigDecimal(amountSymbol, billDetailVO.getSubTotalAmount()));
                vo.setDiscountAmount(vo.formatBigDecimal(amountSymbol, billDetailVO.getDiscountAmount()));
                vo.setAmountWithTax(vo.formatBigDecimal(amountSymbol, billDetailVO.getAmountWithTax()));
                vo.setAmountWithoutTax(vo.formatBigDecimal(amountSymbol, billDetailVO.getAmountWithoutTax()));
                vo.setBillingStartTime(billDetailVO.getBillingStartTime());
                vo.setBillingEndTime(billDetailVO.getBillingEndTime());
                vo.setShowCoupons(billDetailVO.getShowCoupons());
            } else {
                MakeupIncomeBillDetailVo billDetailVO = makeupBillService.queryInvoiceBillDetail(billReqDto);
                vo.setShowContents(billDetailVO.getShowContents());
                vo.setBillType(InvoiceEnum.BillType.MAKEUP.getCode());
                vo.setTaxAmount(vo.formatBigDecimal(amountSymbol, billDetailVO.getTaxAmount()));
                vo.setSubTotalAmount(vo.formatBigDecimal(amountSymbol, billDetailVO.getSubTotalAmount()));
                vo.setDiscountAmount(vo.formatBigDecimal(amountSymbol, billDetailVO.getDiscountAmount()));
                vo.setAmountWithTax(vo.formatBigDecimal(amountSymbol, billDetailVO.getAmountWithTax()));
                vo.setAmountWithoutTax(vo.formatBigDecimal(amountSymbol, billDetailVO.getAmountWithoutTax()));
                vo.setBillingStartTime(billDetailVO.getBillingStartTime());
                vo.setBillingEndTime(billDetailVO.getBillingEndTime());
                vo.setShowCoupons(billDetailVO.getShowCoupons());
            }
            vo.setStartingDateOfBill(formatDateRange(vo.getBillingStartTime(), vo.getBillingEndTime()));

            // 处理账单 金额符号
//            convertBillContentList(vo.getShowContents());
//
//            // 处理优惠 金额符号
//            if (vo.getShowCoupons() != null && !vo.getShowCoupons().isEmpty()) {
//                for (BillCoupon showCoupon : vo.getShowCoupons()) {
//                    showCoupon.setTotalAmount(amountSymbol + showCoupon.getTotalAmount());
//                    showCoupon.setDiscountPrice(amountSymbol + showCoupon.getDiscountPrice());
//                }
//            }

            // 构造假数据
            List<Map<String, Object>> maps = Test.generateSampleData();
            List<BillContent> collect = maps.stream()
                    .map(map -> BeanUtil.mapToBean(map, BillContent.class, false))
                    .collect(Collectors.toList());
            vo.setShowContents(collect);
            List<BillCoupon> showCoupons = new ArrayList<>();
            BillCoupon b1 = new BillCoupon();
            b1.setDescription("AI智能通信折扣(10%)");
            b1.setDiscountPrice("-S$ 2.50");
            b1.setTotalAmount("-S$ 2.50");
            BillCoupon b2 = new BillCoupon();
            b2.setDescription("二次优惠折扣(- S$ 2.00)");
            b2.setDiscountPrice("-S$ 2.50");
            b2.setTotalAmount("-S$ 2.50");
            showCoupons.add(b1);
            showCoupons.add(b2);
            vo.setShowCoupons(showCoupons);
        }
        return vo;
    }

    /**
     * 审核
     *
     * @param dto
     * @return
     */
    @Override
    public CommonResult<Long> toExamine(InvoiceToExamineDTO dto) {
        ChargeInvoiceDO invoiceDO = invoiceMapper.selectById(dto.getId());
        if (invoiceDO != null) {
            if (dto.getStatus() == 1) {

                // 退还可开票金额
                BillInvoiceAmountCancelDTO cancelDTO = new BillInvoiceAmountCancelDTO();
                cancelDTO.setInvoiceId(invoiceDO.getId());
                cancelDTO.setInvoiceAmount(invoiceDO.getInvoiceAmount());
                cancelDTO.setServiceCode(invoiceDO.getBillServiceCode());
                cancelDTO.setBillingTime(invoiceDO.getBillTime());
                cancelDTO.setBillDetailId(Long.valueOf(invoiceDO.getInvoiceBillingId()));

                // 根据账单类型 选择对应的账单服务进行退费操作
                Integer billType = invoiceDO.getBillType();
                if (InvoiceEnum.BillType.PREPAID.is(billType)) {
                    prepaidService.cancelInvoice(cancelDTO);
                } else if (InvoiceEnum.BillType.POSTPAID.is(billType)) {
                    postpaidService.cancelInvoice(cancelDTO);
                } else {
                    makeupBillService.cancelInvoice(cancelDTO);
                }
                invoiceDO.setStatus(dto.getStatus());
            } else {

                // 通过 判断是否为信用票据
                invoiceDO.setStatus(dto.getStatus());
                if (invoiceDO.getInvoiceType() == 0) {

                    // 退回开票金额 作废原始票据
                    Integer billType = invoiceDO.getBillType();
                    boolean refundFlag = false;
                    if (InvoiceEnum.BillType.PREPAID.is(billType)) {
                        refundFlag = prepaidService.refundInvoice(Long.valueOf(invoiceDO.getInvoiceBillingId()), invoiceDO.getBillTime(), invoiceDO.getInvoiceAmount());
                    } else if (InvoiceEnum.BillType.POSTPAID.is(billType)) {
                        refundFlag = postpaidService.refundInvoice(Long.valueOf(invoiceDO.getInvoiceBillingId()), invoiceDO.getBillTime(), invoiceDO.getInvoiceAmount());
                    } else {
                        refundFlag = makeupBillService.refundInvoice(Long.valueOf(invoiceDO.getInvoiceBillingId()), invoiceDO.getBillTime(), invoiceDO.getInvoiceAmount());
                    }
                    if (!refundFlag) {
                        log.info("生成信用票据退回账单金额失败, 账单ID: {}", invoiceDO.getId());
                        invoiceDO.setStatus(1);
                    } else {
                        invoiceDO.setStatus(dto.getStatus());
                    }
                }
            }
            invoiceDO.setUpdateTime(System.currentTimeMillis());
            LoginUser user = WebFrameworkUtils.getLoginUser();
            if (user != null) {
                invoiceDO.setUpdater(user.getUsername());
            }
            invoiceMapper.updateById(invoiceDO);
        }
        return CommonResult.success();
    }

    /**
     * 发送邮箱
     *
     * @param dto
     * @return
     */
    @Override
    public Boolean sendMail(InvoiceSendMailDTO dto) {
        if (!Validator.isEmail(dto.getToMail())) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.SEND_MAIL_ADDRESS_ERROR);
        }

        // 获取发票基础数据
        InvoiceDetailsResVO details = details(dto.getId());
        if (details.getStatus() != 2) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.INVOICE_STATUS_ERROR);
        }

        // 生成pdf文件
        byte[] bytes = InvoiceConvertPdfUtil.createPdf(details, "bill.css", "bill.ftl");
        if (bytes == null || bytes.length == 0) {
            log.info("发票生成pdf文件失败，发票ID: {}", dto.getId());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.PDF_CONTENT_IS_EMPTY);
        }

        // 发送邮箱
        MailSendSingleToUserReqDTO reqDTO = new MailSendSingleToUserReqDTO();
        reqDTO.setMail(dto.getToMail());
        reqDTO.setTemplateCode("invoice-attachment-code");
        Map<String, Object> map = new HashMap<>();
        map.put("code", "INVOICE: " + dto.getId());
        reqDTO.setTemplateParams(map);
        MailSendAttachmentsDTO attachmentsDTO = new MailSendAttachmentsDTO();
        attachmentsDTO.setFiles(bytes);
        attachmentsDTO.setData(JSON.toJSONString(reqDTO));
        CommonResult<Boolean> longCommonResult = mailSendApi.sendAttachments(attachmentsDTO);
        log.info("发送邮箱结果返回：{}", JSON.toJSONString(longCommonResult));
        if (longCommonResult == null || !longCommonResult.getData()) {
            return false;
        }
        return true;
    }

    /**
     * 信用票据
     *
     * @param dto
     * @return
     */
    @Override
    public CommonResult<String> creditInvoice(CreditInvoiceDTO dto) {

        // 获取原始票据
        ChargeInvoiceDO invoiceDO = invoiceMapper.selectById(dto.getId());
        if (invoiceDO == null || invoiceDO.getStatus() != 2 || invoiceDO.getInvoiceType() == 0) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.CREDIT_NOTE_ERROR);
        }

        // 查询是否存在已经开出的信息票据
        if (invoiceDO.getCreditInvoiceId() != null) {
            ChargeInvoiceDO creditDo = invoiceMapper.selectById(invoiceDO.getCreditInvoiceId());
            if (creditDo.getInvoiceType() == 0 && creditDo.getStatus() != 1) {
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.CREDIT_EXIST_ERROR);
            }
        }

        // 生成信用票据记录
        String invoiceId = invoiceIdGenerator.generateId(invoiceDO.getEntityId(), "CN-");
        ChargeInvoiceDO invoiceDONew = new ChargeInvoiceDO();
        BeanUtil.copyProperties(invoiceDO, invoiceDONew);
        invoiceDONew.setId(IdUtil.getSnowflakeNextId());

        // 清空基础数据
        invoiceDONew.clearBaseDO();
        LoginUser user = WebFrameworkUtils.getLoginUser();
        if (user != null) {
            invoiceDONew.setCreator(user.getUsername());
        }
        invoiceDONew.setCreateTime(System.currentTimeMillis());
        invoiceDONew.setInvoiceType(0);
        invoiceDONew.setInvoiceId(invoiceId);
        invoiceDONew.setStatus(0);
        invoiceMapper.insert(invoiceDONew);
        invoiceDO.setCreditInvoiceId(invoiceDONew.getId());
        invoiceMapper.updateById(invoiceDO);
        return CommonResult.success("ture");
    }

    /**
     * 下载发票
     *
     * @param id
     * @param response
     */
    @Override
    public void download(Long id, HttpServletResponse response) {

        // 获取发票基础数据
        InvoiceDetailsResVO details = details(id);

        // 判断发票状态
        if (details.getStatus() != 2) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.INVOICE_STATUS_ERROR);
        }

        // 生成pdf文件
        byte[] bytes = InvoiceConvertPdfUtil.createPdf(details, "bill.css", "bill.ftl");
        if (bytes == null || bytes.length == 0) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.PDF_CONTENT_IS_EMPTY);
        }
        try (OutputStream out = response.getOutputStream()) {

            // 2. 设置响应头
            response.setContentType("application/pdf");

            // 使用URL编码处理文件名，避免中文乱码
            String encodedFileName = URLEncoder.encode(System.currentTimeMillis() + ".pdf", StandardCharsets.UTF_8).replace("+", "%20");
            response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);
            response.setContentLength(bytes.length);

            // 3. 将文件写入响应流
            out.write(bytes);
            out.flush();
        } catch (Exception ignored) {
            log.error("下载发票pdf文件异常：", ignored);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.PDF_DOWNLOAD_FILE_ERROR);
        }
    }

    private static String formatDateWithOrdinalNew(long timestamp) {
        Instant instant = Instant.ofEpochMilli(timestamp);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("d MMM yyyy", Locale.ENGLISH)
                .withZone(ZoneId.systemDefault());
        String baseDate = formatter.format(instant);
        return addOrdinalSuffix(baseDate);
    }

    // 添加序数后缀（1st, 2nd, 3rd, 4th...）
    private static String addOrdinalSuffix(String dateStr) {
        String[] parts = dateStr.split(" ");
        int day = Integer.parseInt(parts[0]);
        String suffix;

        if (day >= 11 && day <= 13) {
            suffix = "th";
        } else {
            switch (day % 10) {
                case 1:
                    suffix = "st";
                    break;
                case 2:
                    suffix = "nd";
                    break;
                case 3:
                    suffix = "rd";
                    break;
                default:
                    suffix = "th";
                    break;
            }
        }

        return day + suffix + " " + parts[1] + " " + parts[2];
    }

    // 处理时间字符串
    public static String formatDateRange(long startTimestamp, long endTimestamp) {
        Date startDate = new Date(startTimestamp);
        Date endDate = new Date(endTimestamp);

        Calendar startCal = Calendar.getInstance();
        startCal.setTime(startDate);

        Calendar endCal = Calendar.getInstance();
        endCal.setTime(endDate);

        // 使用 Locale.US 确保月份显示为英文缩写（Mar, Apr）
        SimpleDateFormat monthDayFormat = new SimpleDateFormat("MMM dd", Locale.US);
        SimpleDateFormat monthDayYearFormat = new SimpleDateFormat("MMM dd,yyyy", Locale.US);

        boolean sameYear = startCal.get(Calendar.YEAR) == endCal.get(Calendar.YEAR);
        boolean sameMonth = sameYear && (startCal.get(Calendar.MONTH) == endCal.get(Calendar.MONTH));

        if (sameMonth) {

            // 同年同月: Apr 13,2025
            return monthDayYearFormat.format(endDate);
        } else if (sameYear) {

            // 同年跨月: Mar 13 - Apr 13,2025
            return monthDayFormat.format(startDate) + " - " + monthDayYearFormat.format(endDate);
        } else {

            // 跨年: Mar 13,2024 - Apr 13,2025
            return monthDayYearFormat.format(startDate) + " - " + monthDayYearFormat.format(endDate);
        }
    }

    public static List<BillContent> convertBillContentList(List<BillContent> billList) {
        return billList.stream()
                .peek(bill -> {

                    // 1. 修改当前对象的属性
                    if (bill.getCurrencySymbol() != null) {
                        if (bill.getDiscountUnitPrice() != null) {
                            bill.setDiscountUnitPrice(bill.getCurrencySymbol() + bill.getDiscountUnitPrice());
                        }
                        if (bill.getDiscountPrice() != null) {
                            bill.setDiscountPrice(bill.getCurrencySymbol() + bill.getDiscountPrice());
                        }
                        if (bill.getTotalAmount() != null) {
                            bill.setTotalAmount(bill.getCurrencySymbol() + bill.getTotalAmount());
                        }
                    }

                    // 2. 递归处理children
                    if (bill.getChildren() != null) {
                        bill.setChildren(convertBillContentList(bill.getChildren()));
                    }
                }).collect(Collectors.toList());
    }

    public static void main(String[] args) {

        // 示例时间戳（毫秒级）
        long timestamp = System.currentTimeMillis(); // 对应 2024-01-01 00:00:00

        // 方法2：使用 DateTimeFormatter（Java 8+ 新API）
        String formattedDateNew = formatDateWithOrdinalNew(timestamp);
        System.out.println("新API: " + formattedDateNew); // 输出: 1st Jan 2024

        // 测试用例
        Calendar cal = Calendar.getInstance();

        // 1. 同年跨月
        cal.set(2025, Calendar.MARCH, 13);
        long start1 = cal.getTimeInMillis();
        cal.set(2025, Calendar.APRIL, 13);
        long end1 = cal.getTimeInMillis();
        System.out.println(formatDateRange(start1, end1)); // Mar 13 - Apr 13,2025

        // 2. 跨年
        cal.set(2024, Calendar.MARCH, 13);
        long start2 = cal.getTimeInMillis();
        cal.set(2025, Calendar.APRIL, 13);
        long end2 = cal.getTimeInMillis();
        System.out.println(formatDateRange(start2, end2)); // Mar 13,2024 - Apr 13,2025

        // 3. 同年同月
        cal.set(2025, Calendar.APRIL, 13);
        long start3 = cal.getTimeInMillis();
        cal.set(2025, Calendar.APRIL, 13);
        long end3 = cal.getTimeInMillis();
        System.out.println(formatDateRange(start3, end3)); // Apr 13,2025
    }
}
