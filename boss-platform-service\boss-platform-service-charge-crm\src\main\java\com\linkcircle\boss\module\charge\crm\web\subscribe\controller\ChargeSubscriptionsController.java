package com.linkcircle.boss.module.charge.crm.web.subscribe.controller;

import com.linkcircle.boss.framework.common.exception.ServiceException;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.common.model.PageParam;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.framework.excel.core.util.ExcelUtils;
import com.linkcircle.boss.module.charge.crm.constants.ErrorCodeConstants;
import com.linkcircle.boss.module.charge.crm.web.scheduled.SubscriptionXxlJobHandler;
import com.linkcircle.boss.module.charge.crm.web.subscribe.model.dto.ChargeSubscriptionsCreateReqDTO;
import com.linkcircle.boss.module.charge.crm.web.subscribe.model.dto.ChargeSubscriptionsPageReqDTO;
import com.linkcircle.boss.module.charge.crm.web.subscribe.model.dto.ChargeSubscriptionsUpdateReqDTO;
import com.linkcircle.boss.module.charge.crm.web.subscribe.model.vo.ChargeSubscriptionsDetailVO;
import com.linkcircle.boss.module.charge.crm.web.subscribe.model.vo.ChargeSubscriptionsPageVO;
import com.linkcircle.boss.module.charge.crm.web.subscribe.service.ChargeSubscriptionsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 订阅管理 Controller
 *
 * <AUTHOR>
 */
@Tag(name = "订阅管理")
@RestController
@RequestMapping("/subscriptions")
@Validated
public class ChargeSubscriptionsController {

    @Resource
    private ChargeSubscriptionsService chargeSubscriptionsService;

    @Resource
    private SubscriptionXxlJobHandler subscriptionXxlJobHandler;

    @GetMapping("/page")
    @Operation(method = "GET", summary = "订阅分页查询")
    public CommonResult<PageResult<ChargeSubscriptionsPageVO>> pageQuery(@ParameterObject @Valid ChargeSubscriptionsPageReqDTO dto) {
        PageResult<ChargeSubscriptionsPageVO> pageResult = chargeSubscriptionsService.page(dto);
        return CommonResult.success(pageResult);
    }

    @GetMapping("/detail")
    @Operation(summary = "获取订阅详情")
    public CommonResult<ChargeSubscriptionsDetailVO> detail(@RequestParam Long id) {
        ChargeSubscriptionsDetailVO result = chargeSubscriptionsService.detail(id);
        return CommonResult.success(result);
    }

    @PostMapping("/create")
    @Operation(summary = "创建订阅")
    public CommonResult<Long> create(@RequestBody @Valid ChargeSubscriptionsCreateReqDTO dto) {
        Long id = chargeSubscriptionsService.create(dto);
        return CommonResult.success(id);
    }

    @PostMapping("/update")
    @Operation(summary = "更新订阅")
    public CommonResult<?> update(@RequestBody @Valid ChargeSubscriptionsUpdateReqDTO dto) {
        return CommonResult.success(chargeSubscriptionsService.update(dto));
    }

    @PostMapping("/edit")
    @Operation(summary = "编辑订阅")
    public CommonResult<?> edit(@RequestBody @Valid ChargeSubscriptionsCreateReqDTO dto) {
        return CommonResult.success(chargeSubscriptionsService.edit(dto));
    }

    @GetMapping("/change-status")
    @Operation(summary = "变更订阅状态")
    public CommonResult<Boolean> changeStatus(@RequestParam Long id,
                                              @RequestParam Integer status) {
        boolean result = chargeSubscriptionsService.changeStatus(id, status);
        return CommonResult.success(result);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除订阅")
    public CommonResult<Boolean> delete(@RequestParam Long id) {
        boolean result = chargeSubscriptionsService.delete(id);
        return CommonResult.success(result);
    }

    // 导出
    @GetMapping("/export")
    @Operation(summary = "订阅列表导出")
    public void export(@ParameterObject ChargeSubscriptionsPageReqDTO queryDTO, HttpServletResponse response) {
        try {
            queryDTO.setPageSize(PageParam.PAGE_SIZE_NONE);
            List<ChargeSubscriptionsPageVO> list = chargeSubscriptionsService.page(queryDTO).getList();
            ExcelUtils.write(response, "订阅信息.xlsx", "订阅信息", ChargeSubscriptionsPageVO.class, list);
        } catch (Exception e) {
            throw new ServiceException(ErrorCodeConstants.SUBSCRIBE_EXPORT_EXCEPTION);
        }
    }


}