<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.linkcircle.boss</groupId>
        <artifactId>boss-platform-service</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>boss-platform-service-bpm</artifactId>

    <name>${project.artifactId}</name>
    <description>
        bpm 包下，业务流程管理（Business Process Management），我们放工作流的功能。
        例如说：流程定义、表单配置、审核中心（我的申请、我的待办、我的已办）等等
        bpm 解释：https://baike.baidu.com/item/BPM/1933

        工作流基于 Flowable 6 实现，分成流程定义、流程表单、流程实例、流程任务等功能模块。
    </description>

    <dependencies>
        <!-- 依赖服务 -->
        <dependency>
            <groupId>com.linkcircle.boss</groupId>
            <artifactId>boss-platform-bpm-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.linkcircle.boss</groupId>
            <artifactId>boss-platform-system-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.linkcircle.boss</groupId>
            <artifactId>boss-platform-charge-crm-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.linkcircle.boss</groupId>
            <artifactId>boss-platform-demo-api</artifactId>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>com.linkcircle.boss</groupId>
            <artifactId>boss-platform-spring-boot-starter-biz-data-permission</artifactId>
        </dependency>

        <dependency>
            <groupId>com.linkcircle.boss</groupId>
            <artifactId>boss-platform-sa-token-spring-boot-starter</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.linkcircle.boss</groupId>
            <artifactId>boss-platform-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>com.linkcircle.boss</groupId>
            <artifactId>boss-platform-spring-boot-starter-rpc</artifactId>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>com.linkcircle.boss</groupId>
            <artifactId>boss-platform-spring-boot-starter-excel</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-starter-logging</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Flowable 工作流相关 -->
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-spring-boot-starter-process</artifactId>
        </dependency>
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-spring-boot-starter-actuator</artifactId>
        </dependency>
    </dependencies>

    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>io.github.git-commit-id</groupId>
                <artifactId>git-commit-id-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
