package com.linkcircle.boss.module.billing.web.detail.rate.common.service;

import com.linkcircle.boss.module.billing.web.data.model.vo.CyclePeriodResultVO;
import com.linkcircle.boss.module.crm.enums.BillTypeEnum;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-08-05 16:21
 * @description 统一费率用量缓存服务接口 - 支持收入和成本两种类型
 */
public interface UnifiedRateUsageCacheService {

    /**
     * 更新套餐费率用量缓存
     *
     * @param billType          账单类型 (INCOME/COST)
     * @param cyclePeriodResult 周期结果
     * @param usage            用量
     * @param usageUnit        用量单位
     * @return 是否成功
     */
    boolean updatePackageRateUsageCache(BillTypeEnum billType, CyclePeriodResultVO cyclePeriodResult, 
                                       BigDecimal usage, String usageUnit);

    /**
     * 更新按量费率用量缓存
     *
     * @param billType          账单类型 (INCOME/COST)
     * @param cyclePeriodResult 周期结果
     * @param usage            用量
     * @param usageUnit        用量单位
     * @return 是否成功
     */
    boolean updateUsageRateUsageCache(BillTypeEnum billType, CyclePeriodResultVO cyclePeriodResult, 
                                     BigDecimal usage, String usageUnit);

    /**
     * 从数据库加载用量数据到缓存
     *
     * @param billType       账单类型 (INCOME/COST)
     * @param rateType       费率类型
     * @param serviceId      服务ID
     * @param entityId       实体ID (收入用subscriptionId，成本用purchaseId)
     * @param billingCycle   计费周期
     * @return 是否成功
     */
    boolean loadUsageFromDatabase(BillTypeEnum billType, String rateType, Long serviceId, 
                                 Long entityId, String billingCycle);

    /**
     * 获取缓存中的总用量
     *
     * @param billType     账单类型 (INCOME/COST)
     * @param rateType     费率类型
     * @param serviceId    服务ID
     * @param entityId     实体ID (收入用subscriptionId，成本用purchaseId)
     * @param billingCycle 计费周期
     * @return 总用量，如果不存在返回null
     */
    BigDecimal getCachedTotalUsage(BillTypeEnum billType, String rateType, Long serviceId,
                                  Long entityId, String billingCycle);

    /**
     * 获取费率用量（优先从缓存获取，缓存不存在则从数据库查询并回写缓存）
     *
     * @param billType     账单类型 (INCOME/COST)
     * @param rateType     费率类型 (package/usage)
     * @param serviceId    服务ID
     * @param entityId     实体ID (收入用subscriptionId，成本用purchaseId)
     * @param billingCycle 计费周期
     * @return 总用量，如果不存在返回0
     */
    BigDecimal getRateUsageWithCache(BillTypeEnum billType, String rateType, Long serviceId,
                                    Long entityId, String billingCycle);
}
