package com.linkcircle.boss.module.charge.fee.web.bill.controller;

import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.permission.PreAuthorize;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.BillReqDto;
import com.linkcircle.boss.module.charge.fee.web.bill.model.dto.BillSendEmailReqDto;
import com.linkcircle.boss.module.charge.fee.web.bill.model.vo.makeup.MakeupIncomeBillDetailVo;
import com.linkcircle.boss.module.charge.fee.web.bill.service.PrepaidService;
import com.linkcircle.boss.module.charge.fee.web.invoice.model.dto.BillDetailReqDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.linkcircle.boss.framework.common.model.CommonResult.success;

/**
 * <AUTHOR>
 * @date 2025/7/3 11:00
 */
@Tag(name = "账单-预付费账单")
@RestController
@RequestMapping("/customer/prepaid-bill")
@Validated
@RequiredArgsConstructor
public class PrepaidBillController {

    private  final PrepaidService prepaidService;

    @PostMapping("/payOff")
    @Operation(summary = "发起预付费账单结清")
    @PreAuthorize("@ss.hasPermission('customer:prepaid-bill:pay-off')")
    public CommonResult<String> payOffBill(@Valid @RequestBody BillReqDto reqDto) {
        prepaidService.payOffBill(reqDto);
        return success();
    }

    @PostMapping("/delete")
    @Operation(summary = "删除预付费账单")
    @PreAuthorize("@ss.hasPermission('customer:prepaid-bill:delete')")
    public CommonResult<String> deleteBill(@Valid@RequestBody BillReqDto reqDto) {
        prepaidService.deleteBill(reqDto);
        return success();
    }


    @PostMapping("/sendMail")
    @Operation(summary = "发送邮箱")
    @PreAuthorize("@ss.hasPermission('customer:prepaid-bill:send-email')")
    public CommonResult<Boolean> sendMail(@RequestBody BillSendEmailReqDto dto) {
        Boolean vo = prepaidService.sendMail(dto);
        return CommonResult.success(vo);
    }
}
