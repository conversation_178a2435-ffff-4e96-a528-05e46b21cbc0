package com.linkcircle.boss.module.billing.web.detail.rate.income.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-08-05 16:21
 * @description 费率用量数据库同步DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RateUsageDbSyncDTO {

    /**
     * 服务ID
     */
    private Long serviceId;

    /**
     * 订阅ID
     */
    private Long subscriptionId;

    /**
     * 计费周期
     */
    private String billingCycle;

    /**
     * 用量
     */
    private BigDecimal usage;

    /**
     * 费率类型 (package/usage)
     */
    private String rateType;

    /**
     * 计费时间戳
     */
    private Long billingTime;

    /**
     * 用量单位
     */
    private String usageUnit;

    /**
     * 货币类型
     */
    private String currency;

    /**
     * 账户ID
     */
    private Long accountId;

    /**
     * 周期开始时间
     */
    private Long cycleStartTime;

    /**
     * 周期结束时间
     */
    private Long cycleEndTime;
}
