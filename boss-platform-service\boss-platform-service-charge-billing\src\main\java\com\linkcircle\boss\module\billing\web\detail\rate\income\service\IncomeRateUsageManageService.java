package com.linkcircle.boss.module.billing.web.detail.rate.income.service;

import com.linkcircle.boss.module.billing.web.data.model.vo.CyclePeriodResultVO;
import com.linkcircle.boss.module.billing.web.detail.rate.cost.model.vo.RateUsageVO;
import com.linkcircle.boss.module.crm.enums.ChargeRateTypeEnum;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-06-23 09:02
 * @description 收入-费率用量管理服务接口
 */
public interface IncomeRateUsageManageService {

    /**
     * 更新费率用量数据
     * 根据计费类型自动选择对应的表进行操作
     *
     * @param cyclePeriodResult 周期计算结果
     * @param usage             用量（阶梯费率、套餐费率、按量计费使用）
     * @param usageUnit         用量单位（阶梯费率、套餐费率、按量计费使用）
     * @param inTrial           是否是试用
     * @return 是否操作成功
     */
    boolean updateRateUsage(CyclePeriodResultVO cyclePeriodResult,
                            BigDecimal usage,
                            String usageUnit,
                            Boolean inTrial,
                            Boolean existRecord);

    /**
     * 查询费率用量数据
     * 根据计费类型自动选择对应的表进行查询
     *
     * @param rateType       计费类型 0-固定费率, 1-阶梯费率, 2-套餐计费, 3-按量计费
     * @param subscriptionId 订阅ID（固定费率、阶梯费率、套餐费率使用）
     * @param accountId      账户ID（按量计费使用）
     * @param serviceId      服务ID（按量计费使用）
     * @param billingCycle   计费周期标识
     * @return 累计用量，固定费率返回null
     */
    RateUsageVO getRateUsage(Integer rateType,
                             Long subscriptionId,
                             Long accountId,
                             Long serviceId,
                             String billingCycle);

    RateUsageVO getRateUsage(ChargeRateTypeEnum rateType,
                             CyclePeriodResultVO cyclePeriodResult);

    /**
     * 检查固定费率是否已计费
     *
     * @param subscriptionId 订阅ID
     * @param billingCycle   计费周期标识
     * @return 是否已计费
     */
    boolean isFixedRateBilled(Long subscriptionId, Long serviceId, String billingCycle);

    /**
     * 检查阶梯费率是否已计费 TIERED
     *
     * @param subscriptionId 订阅ID
     * @param billingCycle   计费周期标识
     * @return 是否已计费
     */
    boolean isTieredRateBilled(Long subscriptionId, Long serviceId, String billingCycle);

}
