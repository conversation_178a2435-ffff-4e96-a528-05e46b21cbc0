package com.linkcircle.boss.module.bpm.service.message;

import com.linkcircle.boss.framework.common.enums.UserTypeEnum;
import com.linkcircle.boss.framework.common.util.json.JsonUtils;
import com.linkcircle.boss.framework.web.config.BpmMessageProperties;
import com.linkcircle.boss.framework.web.config.WebProperties;
import com.linkcircle.boss.module.bpm.convert.message.BpmMessageConvert;
import com.linkcircle.boss.module.bpm.enums.message.BpmMessageEnum;
import com.linkcircle.boss.module.bpm.service.message.dto.BpmMessageSendWhenProcessInstanceApproveReqDTO;
import com.linkcircle.boss.module.bpm.service.message.dto.BpmMessageSendWhenProcessInstanceRejectReqDTO;
import com.linkcircle.boss.module.bpm.service.message.dto.BpmMessageSendWhenTaskCreatedReqDTO;
import com.linkcircle.boss.module.bpm.service.message.dto.BpmMessageSendWhenTaskTimeoutReqDTO;
import com.linkcircle.boss.module.system.api.mail.MailSendApi;
import com.linkcircle.boss.module.system.api.notify.NotifyMessageSendApi;
import com.linkcircle.boss.module.system.api.sms.SmsSendApi;
import com.linkcircle.boss.module.system.api.websocket.WebSocketSenderApi;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.HashMap;
import java.util.Map;

/**
 * BPM 消息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class BpmMessageServiceImpl implements BpmMessageService {

    @Resource
    private SmsSendApi smsSendApi;
    @Resource
    private WebSocketSenderApi webSocketSenderApi;
    @Resource
    private NotifyMessageSendApi notifyMessageSendApi;
    @Resource
    private MailSendApi mailSendApi;
    @Resource
    private WebProperties webProperties;
    @Resource
    private BpmMessageProperties bpmMessageProperties;

    @Override
    public void sendMessageWhenProcessInstanceApprove(BpmMessageSendWhenProcessInstanceApproveReqDTO reqDTO) {
        Map<String, Object> templateParams = new HashMap<>();
        templateParams.put("processInstanceName", reqDTO.getProcessInstanceName());
        templateParams.put("detailUrl", getProcessInstanceDetailUrl(reqDTO.getProcessInstanceId()));

        sendAllMessageTypes(reqDTO.getStartUserId(), BpmMessageEnum.PROCESS_INSTANCE_APPROVE, templateParams);
    }

    @Override
    public void sendMessageWhenProcessInstanceReject(BpmMessageSendWhenProcessInstanceRejectReqDTO reqDTO) {
        Map<String, Object> templateParams = new HashMap<>();
        templateParams.put("processInstanceName", reqDTO.getProcessInstanceName());
        templateParams.put("reason", reqDTO.getReason());
        templateParams.put("detailUrl", getProcessInstanceDetailUrl(reqDTO.getProcessInstanceId()));

        sendAllMessageTypes(reqDTO.getStartUserId(), BpmMessageEnum.PROCESS_INSTANCE_REJECT, templateParams);
    }

    @Override
    public void sendMessageWhenTaskAssigned(BpmMessageSendWhenTaskCreatedReqDTO reqDTO) {
        Map<String, Object> templateParams = new HashMap<>();
        templateParams.put("processInstanceName", reqDTO.getProcessInstanceName());
        templateParams.put("taskName", reqDTO.getTaskName());
        templateParams.put("startUserNickname", reqDTO.getStartUserNickname());
        templateParams.put("detailUrl", getProcessInstanceDetailUrl(reqDTO.getProcessInstanceId()));

        sendAllMessageTypes(reqDTO.getAssigneeUserId(), BpmMessageEnum.TASK_ASSIGNED, templateParams);
    }

    @Override
    public void sendMessageWhenTaskTimeout(BpmMessageSendWhenTaskTimeoutReqDTO reqDTO) {
        Map<String, Object> templateParams = new HashMap<>();
        templateParams.put("processInstanceName", reqDTO.getProcessInstanceName());
        templateParams.put("taskName", reqDTO.getTaskName());
        templateParams.put("detailUrl", getProcessInstanceDetailUrl(reqDTO.getProcessInstanceId()));

        sendAllMessageTypes(reqDTO.getAssigneeUserId(), BpmMessageEnum.TASK_TIMEOUT, templateParams);
    }

    /**
     * 发送所有类型的消息
     * @param userId 用户ID
     * @param messageEnum 消息枚举
     * @param templateParams 模板参数
     */
    private void sendAllMessageTypes(Long userId, BpmMessageEnum messageEnum, Map<String, Object> templateParams) {
        // 发送WebSocket消息
        if (bpmMessageProperties.getEnableWs()) {
            webSocketSenderApi.send(
                    UserTypeEnum.ADMIN.getValue(),
                    userId,
                    messageEnum.getWsMessageType(),
                    JsonUtils.toJsonString(templateParams)
            );
        }

        // 发送短信
        if (bpmMessageProperties.getEnableSms()) {
            smsSendApi.sendSingleSmsToAdmin(BpmMessageConvert.INSTANCE.convert(userId,
                    messageEnum.getSmsTemplateCode(), templateParams)).checkError();
        }

        // 发送系统通知
        if (bpmMessageProperties.getEnableNotify()) {
            notifyMessageSendApi.sendSingleMessageToAdmin(BpmMessageConvert.INSTANCE.convert2NotifyDTO(userId,
                    messageEnum.getNotifyTemplateCode(), templateParams)).checkError();
        }

        // 发送邮件
        if (bpmMessageProperties.getEnableMail()) {
            mailSendApi.sendSingleMailToAdmin(BpmMessageConvert.INSTANCE.convert2MailDTO(
                    userId,
                    messageEnum.getMailTemplateCode(),
                    templateParams)).checkError();
        }

    }

    private String getProcessInstanceDetailUrl(String taskId) {
        return webProperties.getAdminUi().getUrl() + "/bpm/process-instance/detail?id=" + taskId;
    }

}
