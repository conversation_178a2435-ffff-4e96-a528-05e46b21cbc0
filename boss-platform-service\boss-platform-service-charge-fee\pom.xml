<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.linkcircle.boss</groupId>
        <artifactId>boss-platform-service</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>boss-platform-service-charge-fee</artifactId>
    <description>
        charge-fee 模块下， 与费用相关的
        收费系统
        支出系统
        支付系统 钱包管理, 对接第三支付接口
        财务/发票 对接第三方开票接口
    </description>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- 依赖服务 -->
        <dependency>
            <groupId>com.linkcircle.boss</groupId>
            <artifactId>boss-platform-system-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.linkcircle.boss</groupId>
            <artifactId>boss-platform-charge-crm-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.linkcircle.boss</groupId>
            <artifactId>boss-platform-charge-billing-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.linkcircle.boss</groupId>
            <artifactId>boss-platform-charge-fee-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.linkcircle.boss</groupId>
            <artifactId>boss-platform-sa-token-spring-boot-starter</artifactId>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>com.linkcircle.boss</groupId>
            <artifactId>boss-platform-spring-boot-starter-biz-data-permission</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.linkcircle.boss</groupId>
            <artifactId>boss-platform-spring-boot-starter-mybatis</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
                    <groupId>com.baomidou</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>druid-spring-boot-3-starter</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>

        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>com.linkcircle.boss</groupId>
            <artifactId>boss-platform-spring-boot-starter-rpc</artifactId>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>com.linkcircle.boss</groupId>
            <artifactId>boss-platform-spring-boot-starter-excel</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-starter-logging</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>io.github.mouzt</groupId>
            <artifactId>bizlog-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.linkcircle.boss</groupId>
            <artifactId>boss-platform-sharding-jdbc-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
        </dependency>

        <!--pdf相关-->
        <dependency>
            <groupId>com.openhtmltopdf</groupId>
            <artifactId>openhtmltopdf-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.openhtmltopdf</groupId>
            <artifactId>openhtmltopdf-pdfbox</artifactId>
        </dependency>
        <dependency>
            <groupId>com.openhtmltopdf</groupId>
            <artifactId>openhtmltopdf-rtl-support</artifactId>
        </dependency>

        <dependency>
            <groupId>com.openhtmltopdf</groupId>
            <artifactId>openhtmltopdf-jsoup-dom-converter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>

        <dependency>
            <groupId>io.github.kk01001</groupId>
            <artifactId>xxl-job-spring-boot-starter</artifactId>
        </dependency>

    </dependencies>

    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>io.github.git-commit-id</groupId>
                <artifactId>git-commit-id-maven-plugin</artifactId>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*</include>
                </includes>
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>

</project>