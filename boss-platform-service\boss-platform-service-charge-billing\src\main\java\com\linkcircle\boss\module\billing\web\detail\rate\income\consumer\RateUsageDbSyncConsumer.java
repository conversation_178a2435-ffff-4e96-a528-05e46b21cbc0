package com.linkcircle.boss.module.billing.web.detail.rate.income.consumer;

import com.linkcircle.boss.framework.common.constants.ChargeTopicConstant;
import com.linkcircle.boss.framework.common.util.json.JsonUtils;
import com.linkcircle.boss.framework.tanant.TenantContextHolder;
import com.linkcircle.boss.framework.web.context.EnableLoginContext;
import com.linkcircle.boss.module.billing.web.detail.rate.income.model.dto.RateUsageDbSyncDTO;
import com.linkcircle.boss.module.billing.web.detail.rate.income.service.RateUsageDbSyncService;
import io.github.kk01001.util.TraceIdUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025-08-05 16:21
 * @description 费率用量数据库同步消费者
 */
@Component
@Slf4j
@RequiredArgsConstructor
@RocketMQMessageListener(
        topic = ChargeTopicConstant.CHARGE_DELAY_TOPIC,
        selectorExpression = ChargeTopicConstant.TAG_RATE_USAGE_DB_SYNC,
        consumerGroup = ChargeTopicConstant.GROUP_RATE_USAGE_DB_SYNC,
        maxReconsumeTimes = 3
)
public class RateUsageDbSyncConsumer implements RocketMQListener<String> {

    private final RateUsageDbSyncService rateUsageDbSyncService;

    @Override
    public void onMessage(String message) {
        String traceId = TraceIdUtil.generateTraceId();
        TraceIdUtil.setTraceId(traceId);
        
        try {
            log.info("接收到费率用量数据库同步消息, message: {}", message);
            
            RateUsageDbSyncDTO syncDTO = JsonUtils.parseObject(message, RateUsageDbSyncDTO.class);
            if (syncDTO == null) {
                log.error("费率用量数据库同步消息解析失败, message: {}", message);
                return;
            }

            // 设置租户上下文
            TenantContextHolder.setTenantId(1L);
            
            // 设置登录用户上下文
            EnableLoginContext.setLoginUserId(1L);
            EnableLoginContext.setLoginUserType(1);

            boolean success = rateUsageDbSyncService.processDbSyncMessage(
                    syncDTO.getServiceId(),
                    syncDTO.getSubscriptionId(),
                    syncDTO.getBillingCycle(),
                    syncDTO.getUsage(),
                    syncDTO.getRateType(),
                    syncDTO.getBillingTime(),
                    syncDTO.getUsageUnit(),
                    syncDTO.getCurrency(),
                    syncDTO.getAccountId(),
                    syncDTO.getCycleStartTime(),
                    syncDTO.getCycleEndTime()
            );

            if (success) {
                log.info("费率用量数据库同步处理成功, rateType: {}, serviceId: {}, subscriptionId: {}, billingCycle: {}", 
                        syncDTO.getRateType(), syncDTO.getServiceId(), syncDTO.getSubscriptionId(), syncDTO.getBillingCycle());
            } else {
                log.error("费率用量数据库同步处理失败, rateType: {}, serviceId: {}, subscriptionId: {}, billingCycle: {}", 
                        syncDTO.getRateType(), syncDTO.getServiceId(), syncDTO.getSubscriptionId(), syncDTO.getBillingCycle());
                throw new RuntimeException("费率用量数据库同步处理失败");
            }
            
        } catch (Exception e) {
            log.error("费率用量数据库同步消息处理异常, message: {}", message, e);
            throw e;
        } finally {
            // 清理上下文
            TenantContextHolder.clear();
            EnableLoginContext.clear();
            TraceIdUtil.clear();
        }
    }
}
