package com.linkcircle.boss.module.report.web.bill.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linkcircle.boss.framework.common.exception.util.ServiceExceptionUtil;
import com.linkcircle.boss.framework.common.model.CommonResult;
import com.linkcircle.boss.framework.common.model.PageParam;
import com.linkcircle.boss.framework.common.model.PageResult;
import com.linkcircle.boss.framework.excel.core.util.ExcelUtils;
import com.linkcircle.boss.framework.mybatis.core.util.MyBatisUtils;
import com.linkcircle.boss.framework.tanant.TenantUtils;
import com.linkcircle.boss.module.billing.api.bill.product.model.dto.BillDiscountConfigDTO;
import com.linkcircle.boss.module.crm.api.basicConfig.BasicConfigApi;
import com.linkcircle.boss.module.crm.api.basicConfig.vo.InvoiceDetailsVO;
import com.linkcircle.boss.module.crm.api.customer.customer.CustomerApi;
import com.linkcircle.boss.module.crm.api.customer.customer.vo.ChargeCustomerInfoVO;
import com.linkcircle.boss.module.crm.api.entity.EntityApi;
import com.linkcircle.boss.module.crm.api.entity.vo.EntityDetailsVO;
import com.linkcircle.boss.module.crm.api.supplier.supplier.vo.SupplierCategoryRespVO;
import com.linkcircle.boss.module.crm.api.supplier.supplier.vo.SupplierResourceRespVO;
import com.linkcircle.boss.module.report.constants.ErrorCodeConstants;
import com.linkcircle.boss.module.report.enums.DownLoadEnum;
import com.linkcircle.boss.module.report.web.bill.convert.MakeupIncomeBillConvert;
import com.linkcircle.boss.module.report.web.bill.mapper.MakeupBillMapper;
import com.linkcircle.boss.module.report.web.bill.mapper.MakeupProductBillMapper;
import com.linkcircle.boss.module.report.web.bill.mapper.MakeupProductServiceBillMapper;
import com.linkcircle.boss.module.report.web.bill.model.dto.BillQueryByCustomerIdsReqDTO;
import com.linkcircle.boss.module.report.web.bill.model.dto.fee.FixedRateConfigFeeDTO;
import com.linkcircle.boss.module.report.web.bill.model.dto.fee.PackageRateConfigFeeDTO;
import com.linkcircle.boss.module.report.web.bill.model.dto.fee.TierRateConfigFeeDTO;
import com.linkcircle.boss.module.report.web.bill.model.dto.fee.UsageBasedRateConfigFeeDTO;
import com.linkcircle.boss.module.report.web.bill.model.dto.makeup.BillQueryPageReqDTO;
import com.linkcircle.boss.module.report.web.bill.model.dto.makeup.MakeupDetailReqDTO;
import com.linkcircle.boss.module.report.web.bill.model.entity.MakeupIncomeBillDO;
import com.linkcircle.boss.module.report.web.bill.model.entity.MakeupProductIncomeBillDO;
import com.linkcircle.boss.module.report.web.bill.model.entity.MakeupProductServiceIncomeBillDO;
import com.linkcircle.boss.module.report.web.bill.model.export.bill.makeup.MakeupBillExportVO;
import com.linkcircle.boss.module.report.web.bill.model.export.bill.makeup.MakeupBillExportVOConvert;
import com.linkcircle.boss.module.report.web.bill.model.vo.BillContent;
import com.linkcircle.boss.module.report.web.bill.model.vo.BillCoupon;
import com.linkcircle.boss.module.report.web.bill.model.vo.makeup.*;
import com.linkcircle.boss.module.report.web.bill.service.BillContentHandler;
import com.linkcircle.boss.module.report.web.bill.service.MakeupBillService;
import com.linkcircle.boss.module.report.web.download.model.dto.OnlineExportEnhanceDTO;
import com.linkcircle.boss.module.report.web.download.service.DownLoadService;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/7/2 16:50
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MakeupBillServiceImpl implements MakeupBillService {

    private final MakeupBillMapper makeupBillMapper;

    private final MakeupProductBillMapper makeupProductBillMapper;

    private final MakeupProductServiceBillMapper makeupProductServiceBillMapper;


    private final CustomerApi customerApi;

    private final EntityApi entityApi;

    private final BillContentHandler billContentHandler;
    private final DownLoadService downLoadService;

    private  final  BasicConfigApi basicConfigApi;

    @Override
    public PageResult<MakeupIncomeBillPageVo> queryBillPage(BillQueryPageReqDTO reqDTO) {
        // 构建分页对象
        Page<?> page = MyBatisUtils.buildPage(reqDTO);
        initPageQueryTime(reqDTO);
        // 执行查询并忽略执行中的异常
        List<MakeupIncomeBillDO> list = TenantUtils.executeIgnore(
                () -> makeupBillMapper.queryBillPage(page, reqDTO)
        );
        // 将分页对象转换为分页结果对象
        return processList(list,page,reqDTO.getStartTimeLong(),reqDTO.getEndTimeLong());
    }

    public PageResult<MakeupIncomeBillPageVo> processList(List<MakeupIncomeBillDO> list,Page<?> page,long startTime,long endTime){
        // 创建结果集
        List<MakeupIncomeBillPageVo> vos = new ArrayList<>();

        // 判断查询结果是否为空
        if (CollectionUtils.isNotEmpty(list)) {
            // 将DO对象转换为VO对象
            vos = list.stream().map(MakeupIncomeBillConvert.INSTANCE::convert).toList();
        }
        PageResult<MakeupIncomeBillPageVo> pageResult = MyBatisUtils.convert2PageResult(page, vos);
        if (CollectionUtils.isNotEmpty(vos)) {
            // 处理账单列
            handBillColumn(vos);

            // 提取所有账单ID
            List<Long> billIds = vos.stream().map(MakeupIncomeBillPageVo::getBillId).toList();

            // 查询服务账单
            List<MakeupProductIncomeBillDO> productBills = TenantUtils.executeIgnore(() -> makeupProductBillMapper.queryByBillIds(billIds, startTime,
                    endTime));

            List<MakeupProductServiceIncomeBillDO> serviceBills = TenantUtils.executeIgnore(() -> makeupProductServiceBillMapper.queryByBillIds(billIds,
                    startTime,
                    endTime));


            if (CollectionUtils.isNotEmpty(productBills) && CollectionUtils.isNotEmpty(serviceBills)) {
                for (MakeupIncomeBillPageVo vo : vos) {
                    Long billId = vo.getBillId();
                    List<MakeupIncomeProductBillPageVo> productVos = productBills.stream().filter(productBill -> productBill.getBillId().equals(billId)).map(MakeupIncomeBillConvert.INSTANCE::convertProduct).sorted(Comparator.comparing(MakeupIncomeProductBillPageVo::getProductBillId)).toList();
                    List<MakeupIncomeProductServiceBillPageVo> serviceVos = serviceBills.stream().filter(serviceBill -> serviceBill.getBillId().equals(billId)).map(MakeupIncomeBillConvert.INSTANCE::convertService).toList();
                    for (MakeupIncomeProductBillPageVo productVo : productVos) {
                        List<MakeupIncomeProductServiceBillPageVo> productServiceVos = serviceVos.stream().filter(serviceVo -> serviceVo.getProductBillId().equals(productVo.getProductBillId())).sorted(Comparator.comparing(MakeupIncomeProductServiceBillPageVo::getProductServiceBillId)).toList();
                        productVo.setServices(productServiceVos);
                    }
                    vo.setProducts(productVos);
                }
                handleBillProductServiceColumn(vos);
                for (MakeupIncomeBillPageVo vo : vos) {
                    vo.setProductNames(vo.getProducts().stream().map(MakeupIncomeProductBillPageVo::getProductName).filter(StringUtils::isNotEmpty).collect(Collectors.toList()));
                    vo.setServiceNames(vo.getProducts().stream().flatMap(t->t.getServices().stream()).sorted(Comparator.comparing(MakeupIncomeProductServiceBillPageVo::getProductId)).map(MakeupIncomeProductServiceBillPageVo::getServiceName).filter(StringUtils::isNotEmpty).collect(Collectors.toList()));
                }
            }
        }
        return pageResult;
    }

    @Override
    public PageResult<MakeupIncomeBillPageVo> queryBillPageByCustomerIds(BillQueryByCustomerIdsReqDTO pageReq) {
        // 构建分页对象
        Page<?> page = MyBatisUtils.buildPage(pageReq);
        //initPageQueryTime(pageReq);
        List<Long> customerIds = processIds(pageReq.getCustomerIds());
        List<Long> accountIds = processIds(pageReq.getAccountIds());
        // 执行查询并忽略执行中的异常
        List<MakeupIncomeBillDO> list = TenantUtils.executeIgnore(
                () -> makeupBillMapper.queryBillPageByCustomerIds(page, customerIds,accountIds)
        );
        // 将分页对象转换为分页结果对象
        return processList(list,page,0L,System.currentTimeMillis());
    }

    @Override
    public MakeupIncomeBillDetailVo queryBill(MakeupDetailReqDTO reqDto) {
        // 查询账单信息
        MakeupIncomeBillDO bill = TenantUtils.executeIgnore(
                () -> makeupBillMapper.selectByIdAndTime(reqDto.getBillId(), reqDto.getBillingTime())
        );
        // 将账单信息转换为展示对象
        MakeupIncomeBillDetailVo showVO = MakeupIncomeBillConvert.INSTANCE.convertDetail(bill);
        // 查询服务账单信息
        List<MakeupProductIncomeBillDO> productBills = TenantUtils.executeIgnore(
                () -> makeupProductBillMapper.queryByBillIds(List.of(reqDto.getBillId()), reqDto.getBillingTime(), reqDto.getBillingTime())
        );
        // 查询服务账单信息
        List<MakeupProductServiceIncomeBillDO> serviceBills = TenantUtils.executeIgnore(
                () -> makeupProductServiceBillMapper.queryByBillIds(List.of(reqDto.getBillId()), reqDto.getBillingTime(), reqDto.getBillingTime())
        );

        handleDiscount(showVO, MakeupIncomeBillDetailVo::getDiscountDetails, MakeupIncomeBillDetailVo::setCoupons);
        handBillColumn(showVO);
        if (CollectionUtils.isNotEmpty(productBills) && CollectionUtils.isNotEmpty(serviceBills)) {
            Long billId = showVO.getBillId();
            List<MakeupIncomeProductBilDetailVo> productVos = productBills.stream().filter(productBill -> productBill.getBillId().equals(billId)).map(MakeupIncomeBillConvert.INSTANCE::convertProductDetail).sorted(Comparator.comparing(MakeupIncomeProductBilDetailVo::getProductBillId)).toList();
            List<MakeupIncomeProductServiceBillDetailVo> serviceVos = serviceBills.stream().filter(serviceBill -> serviceBill.getBillId().equals(billId)).map(MakeupIncomeBillConvert.INSTANCE::convertServiceDetail)

                    .toList();
            handleDiscount(productVos, MakeupIncomeProductBilDetailVo::getDiscountDetails, MakeupIncomeProductBilDetailVo::setCoupons);
            for (MakeupIncomeProductBilDetailVo productVo : productVos) {
                List<MakeupIncomeProductServiceBillDetailVo> productServiceVos = serviceVos.stream().filter(serviceVo -> serviceVo.getProductBillId().equals(productVo.getProductBillId())).sorted(Comparator.comparing(MakeupIncomeProductServiceBillDetailVo::getProductServiceBillId)).toList();
                productVo.setServices(productServiceVos);
                // 处理费率详情
                handleDiscount(productServiceVos, MakeupIncomeProductServiceBillDetailVo::getDiscountDetails, MakeupIncomeProductServiceBillDetailVo::setCoupons);
                // 处理费率详情
                handleRateDetail(productServiceVos,
                        MakeupIncomeProductServiceBillDetailVo::getRateDetails,
                        MakeupIncomeProductServiceBillDetailVo::getBillingType,
                        MakeupIncomeProductServiceBillDetailVo::getTaxRate,
                        MakeupIncomeProductServiceBillDetailVo::setFixedRateConfig,
                        MakeupIncomeProductServiceBillDetailVo::setPackageRateConfig,
                        MakeupIncomeProductServiceBillDetailVo::setTierRateConfig,
                        MakeupIncomeProductServiceBillDetailVo::setUsageBasedRateConfig
                );
            }
            showVO.setProducts(productVos);
            handleBillProductServiceColumn(showVO);
        }
        // 查询客户信息
        CommonResult<ChargeCustomerInfoVO> commonResult = customerApi.findById(bill.getCustomerId());
        // 判断客户查询是否成功
        if (commonResult.isSuccess()) {
            // 获取客户信息并设置到展示对象
            ChargeCustomerInfoVO customer = commonResult.getData();
            showVO.setCustomer(customer);
        }
        // 查询实体信息
        CommonResult<EntityDetailsVO> entityApiById = entityApi.findById(bill.getCustomerId());
        // 判断实体查询是否成功
        if (entityApiById.isSuccess()) {
            // 获取实体信息并设置到展示对象
            EntityDetailsVO entity = entityApiById.getData();
            showVO.setEntity(entity);
        }
        processBillConfigDetail(showVO,MakeupIncomeBillDetailVo::getEntityId, MakeupIncomeBillDetailVo::setBillConfigDetails);


        processBillContentAndCoupons(showVO);
        return showVO;
    }



    private void processBillContentAndCoupons(MakeupIncomeBillDetailVo showVO) {
        List<BillDiscountConfigDTO> billCoupons = showVO.getCoupons();
        List<BillCoupon> coupons = new ArrayList<>(billContentHandler.handleCoupons(billCoupons, null, null, showVO.getCurrency()));
        List<BillContent> contents = new ArrayList<>();
        for (MakeupIncomeProductBilDetailVo product : showVO.getProducts()) {
            coupons.addAll(billContentHandler.handleCoupons(product.getCoupons(),product.getProductId(),null,showVO.getCurrency()));
            BillContent productContent = new BillContent();
            productContent.setDescription(StringUtils.isNotEmpty(product.getProductName())?product.getProductName():"");
            for (MakeupIncomeProductServiceBillDetailVo service : product.getServices()) {
                coupons.addAll(billContentHandler.handleCoupons(service.getCoupons(),product.getProductId(),service.getServiceId(),showVO.getCurrency()));
                billContentHandler.handleContent(productContent,service);
            }
            contents.add(productContent);
        }
        showVO.setShowCoupons(coupons);
        showVO.setShowContents(contents);
    }

    public<T> void processBillConfigDetail(T showVO,Function<T,Long> entityIdMapper, BiConsumer<T,InvoiceDetailsVO> handler){
        CommonResult<InvoiceDetailsVO> invoiceDetailsVOCommonResult = basicConfigApi.queryInvoice(entityIdMapper.apply(showVO), 1);
        if(invoiceDetailsVOCommonResult.isSuccess()){
            InvoiceDetailsVO invoiceDetailsVO = invoiceDetailsVOCommonResult.getData();
            if(invoiceDetailsVO!=null){
                handler.accept(showVO,invoiceDetailsVO);
            }
        }
    }

    @Override
    public void exportBill(BillQueryPageReqDTO reqDTO, HttpServletResponse response) {
        try {
            // ③ 将查询结果转换为导出所需的VO列表
            OnlineExportEnhanceDTO<MakeupIncomeBillPageVo, MakeupBillExportVO> online = new OnlineExportEnhanceDTO<>();
            online.pageSize(DownLoadEnum.PAGE_SIZE_1000)
                    .fileName("手动账单导出")
                    .sheetName("账单列表")
                    .exportClass(MakeupBillExportVO.class)
                    .response(response)
                    .pageParam(reqDTO)
                    .queryPageFunc(p -> queryBillPage((BillQueryPageReqDTO) p))
                    .convertFun(MakeupBillExportVOConvert.INSTANCE::convert)
                    .handle(ts -> {});
            downLoadService.exportOnline(online);
        } catch (Exception e) {
            log.error("导出供应商信息异常", e);
            // ⑤ 如果发生IO异常，则抛出ServiceException异常
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.MAKEUP_BILL_EXPORT_ERROR);
        }
    }

}
