package com.linkcircle.boss.module.charge.crm.web.subscribe.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 创建/更新订阅请求 DTO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "更新订阅请求")
public class ChargeSubscriptionsUpdateReqDTO {

    @Schema(description = "订阅ID 必填")
    private Long id;


    /**
     * 发起人自选审批人 Map
     *
     * key：taskKey 任务编码
     * value：审批人的数组
     * 例如：{ taskKey1 :[1, 2] }，则表示 taskKey1 这个任务，提前设定了，由 userId 为 1,2 的用户进行审批
     */
    @Schema(description = "发起人自选审批人 Map")
    private Map<String, List<Long>> startUserSelectAssignees;

    @Schema(description = "订阅详情内容")
    private List<ChargeSubscriptionsTimeDetailsUpdateDTO> subscriptions;
}