package com.linkcircle.boss.module.charge.fee.web.bill.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/8/5 10:18
 */
@Schema(description = "账单发送邮箱-请求参数 Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BillSendEmailReqDto {

    /**
     * 接收邮箱地址
     */
    @Schema(description = "接收方邮箱", requiredMode = Schema.RequiredMode.REQUIRED, example = "<EMAIL>")
    private String toMail;

    @Schema(description = "账单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    private Long billId;

    @Schema(description = "账单时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long billingTime;
}
